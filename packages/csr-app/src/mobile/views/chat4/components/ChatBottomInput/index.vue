<template>
  <div class="chat-bottom-input" :class="{ expanded: isExpanded }">
    <!-- 主输入区域 -->
    <div class="input-section">
      <div class="input-container">
        <!-- 输入框 -->
        <div class="input-field">
          <input
            ref="inputRef"
            v-model="messageText"
            type="text"
            placeholder="Chat here"
            class="message-input"
            @keyup.enter="handleSendMessage"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            :disabled="isLoading"
          />
          <!-- 发送按钮 -->
          <button
            class="send-button"
            @click="handleSendMessage"
            :disabled="!messageText.trim() || isLoading"
            :class="{ active: messageText.trim() }"
          >
            <SendIcon class="send-icon" />
          </button>
        </div>

        <!-- 展开/收起按钮 -->
        <button
          class="expand-button"
          @click="toggleExpanded"
          :class="{ expanded: isExpanded }"
        >
          <ExpandIcon v-if="!isExpanded" class="expand-icon" />
          <CollapseIcon v-else class="collapse-icon" />
        </button>
      </div>
    </div>

    <!-- 功能区域 (展开状态) -->
    <Transition name="slide-up">
      <div v-if="isExpanded" class="features-section">
        <div class="features-grid">
          <div
            v-for="feature in featureItems"
            :key="feature.key"
            class="feature-item"
            @click="handleFeatureClick(feature.key)"
          >
            <div class="feature-icon">
              <component :is="feature.icon" class="svg-icon" />
              <!-- 好感度解锁等级标记 -->
              <div
                v-if="
                  (feature.unlockCheckSceneId || feature.sceneId) &&
                  isHeartValueRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  !isSceneUnlocked(
                    feature.unlockCheckSceneId || feature.sceneId,
                  )
                "
                class="unlock-level-badge"
              >
                {{
                  getSceneRequiredLevelText(
                    feature.unlockCheckSceneId || feature.sceneId,
                  )
                }}
              </div>
              <!-- 金币解锁标记（有试用记录后需要付费） -->
              <div
                v-else-if="
                  (feature.unlockCheckSceneId || feature.sceneId) &&
                  isCoinsRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  hasAnyTrialRecord()
                "
                class="payment-badge"
              >
                <img
                  src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                  class="diamond-icon"
                />
                {{
                  getSceneRequiredCoins(
                    feature.unlockCheckSceneId || feature.sceneId,
                  )
                }}
              </div>
              <!-- 金币解锁场景第一次免费标记 -->
              <div
                v-else-if="
                  (feature.unlockCheckSceneId || feature.sceneId) &&
                  isCoinsRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  !hasAnyTrialRecord()
                "
                class="free-badge"
              >
                FREE
              </div>
              <!-- Chat4 付费标记（其他已解锁但需要付费的场景） -->
              <div
                v-else-if="
                  feature.paymentKey &&
                  !isHeartValueRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  !isCoinsRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  isSceneUnlocked(feature.sceneId) &&
                  sceneButtonStates[feature.paymentKey]?.requiresPayment
                "
                class="payment-badge"
              >
                <img
                  src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                  class="diamond-icon"
                />
                {{ sceneButtonStates[feature.paymentKey]?.coinsRequired || 0 }}
              </div>
              <!-- Chat4 免费试用标记（其他已解锁且第一次免费的场景） -->
              <div
                v-else-if="
                  feature.paymentKey &&
                  !isHeartValueRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  !isCoinsRequirement(
                    feature.unlockCheckSceneId || feature.sceneId,
                  ) &&
                  isSceneUnlocked(feature.sceneId) &&
                  !sceneButtonStates[feature.paymentKey]?.requiresPayment
                "
                class="free-badge"
              >
                FREE
              </div>
            </div>
            <span class="feature-text">{{ feature.text }}</span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SendIcon from '@/assets/icon/chat-send-button.svg'
import ExpandIcon from '@/assets/icon/expand-icon.svg'
import CollapseIcon from '@/assets/icon/collapse-icon.svg'

// 导入新的SVG图标
import LiveIcon from '@/assets/icon/live-icon.svg'
import VideoIcon from '@/assets/icon/video-icon.svg'
import GiftIcon from '@/assets/icon/gift-icon.svg'
import MeetupIcon from '@/assets/icon/meetup-icon.svg'
import MonitorIcon from '@/assets/icon/monitor-icon.svg'
import DanceIcon from '@/assets/icon/dance-icon.svg'
import ConcertIcon from '@/assets/icon/concert-icon.svg'
import MomentIcon from '@/assets/icon/moment-icon.svg'
import DiaryIcon from '@/assets/icon/diary-icon.svg'

import { useSceneUnlock, SCENE_ID_MAP } from '../../composables/useSceneUnlock'
import { useScenePayment } from '../../composables/useScenePayment'
import { useChatEventsStore } from '@/store/chat-events'
import { SceneUnlockUtils } from '@/types/favorability'
import { Chat4SceneId } from '@/types/chat4-scene'

// 场景键常量，避免硬编码
const SCENE_KEYS = {
  LIVE: 'live',
  VIDEO: 'video',
  GIFT: 'gift',
  MAP: 'map',
  MEETUP: 'meetup',
  MONITOR: 'monitor',
  DANCE: 'dance',
  CONCERT: 'concert',
  MOMENT: 'moment',
  DIARY: 'diary',
} as const

// 功能项配置
const featureItems = computed(() => {
  const allItems = [
    {
      key: SCENE_KEYS.LIVE,
      icon: LiveIcon,
      text: 'Live',
      sceneId: SCENE_ID_MAP.live,
      paymentKey: 'Living',
    },
    {
      key: SCENE_KEYS.VIDEO,
      icon: VideoIcon,
      text: 'FaceTime',
      sceneId: SCENE_ID_MAP.video,
      paymentKey: 'Video',
    },
    {
      key: SCENE_KEYS.GIFT,
      icon: GiftIcon,
      text: 'Gift',
      sceneId: SCENE_ID_MAP.gift,
      paymentKey: null, // Gift 没有付费限制
    },
    {
      key: SCENE_KEYS.MEETUP,
      icon: MeetupIcon,
      text: 'Meet up',
      sceneId: SCENE_ID_MAP.map, // 跳转到地图场景
      unlockCheckSceneId: 'Meetup', // 但解锁条件检查Meetup场景
      paymentKey: 'Meetup',
    },
    {
      key: SCENE_KEYS.MONITOR,
      icon: MonitorIcon,
      text: 'Monitor',
      sceneId: SCENE_ID_MAP.monitor,
      paymentKey: 'Monitor',
    },
    {
      key: SCENE_KEYS.MOMENT,
      icon: MomentIcon,
      text: 'Moments',
      sceneId: SCENE_ID_MAP.moment,
      paymentKey: 'Moment',
    },
    {
      key: SCENE_KEYS.DIARY,
      icon: DiaryIcon,
      text: 'Diary',
      sceneId: SCENE_ID_MAP.diary,
      paymentKey: 'Diary',
    },
    // 注释掉的功能项可以在这里添加
    // {
    //   key: SCENE_KEYS.DANCE,
    //   icon: DanceIcon,
    //   text: 'Dance',
    //   sceneId: SCENE_ID_MAP.dance,
    //   paymentKey: null,
    // },
    // {
    //   key: SCENE_KEYS.CONCERT,
    //   icon: ConcertIcon,
    //   text: 'Concert',
    //   sceneId: SCENE_ID_MAP.concert,
    //   paymentKey: null,
    // },
  ]

  // Chat4 根据配置过滤场景按钮
  return allItems.filter((item: any) => {
    // 获取场景所需钻石数量，优先使用 sceneId，否则使用 key
    const sceneId =
      item.sceneId ||
      SCENE_ID_MAP[item.key as keyof typeof SCENE_ID_MAP] ||
      item.key
    const coinsRequired = getSceneCoinsRequired(sceneId)

    // 如果配置为 -1，则隐藏该按钮
    return coinsRequired !== -1
  })
})

interface Props {
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<{
  'send-message': [message: string]
  'feature-click': [feature: string]
  'input-focus': []
  'input-blur': []
  'expand-change': [expanded: boolean]
}>()

// 状态
const messageText = ref('')
const isExpanded = ref(false)
const isLoading = ref(false)
const inputRef = ref<HTMLInputElement>()

// 场景解锁状态管理
const { isSceneUnlocked, getSceneRequiredLevelText } = useSceneUnlock()

// 场景付费状态管理（chat4使用）
const { sceneButtonStates, getSceneCoinsRequired, hasAnyTrialRecord } =
  useScenePayment()

// 判断场景是否需要好感度解锁
const isHeartValueRequirement = (sceneId: string): boolean => {
  const favorabilityState = useChatEventsStore().favorabilityState

  if (!favorabilityState || !favorabilityState.sceneConditions) {
    return false
  }

  const condition = favorabilityState.sceneConditions.find(
    (c: any) => c.scene_id === sceneId,
  )

  return condition?.requirement === 'heart_value'
}

// 判断场景是否需要金币解锁
const isCoinsRequirement = (sceneId: string): boolean => {
  const favorabilityState = useChatEventsStore().favorabilityState

  if (!favorabilityState || !favorabilityState.sceneConditions) {
    return false
  }

  const condition = favorabilityState.sceneConditions.find(
    (c: any) => c.scene_id === sceneId,
  )

  return condition?.requirement === 'coins'
}

// 获取场景所需的金币数量（从场景条件中获取）
const getSceneRequiredCoins = (sceneId: string): number => {
  const favorabilityState = useChatEventsStore().favorabilityState

  if (!favorabilityState || !favorabilityState.sceneConditions) {
    return 0
  }

  const condition = favorabilityState.sceneConditions.find(
    (c: any) => c.scene_id === sceneId && c.requirement === 'coins',
  )

  return condition?.value || 0
}

// 方法
const handleSendMessage = () => {
  const message = messageText.value.trim()
  if (!message || isLoading.value || props.disabled) return

  emit('send-message', message)
  messageText.value = ''
}

const handleFeatureClick = (feature: string) => {
  emit('feature-click', feature)
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('expand-change', isExpanded.value)
}

const handleInputFocus = () => {
  emit('input-focus')
}

const handleInputBlur = () => {
  emit('input-blur')
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  collapse: () => {
    isExpanded.value = false
    emit('expand-change', false)
  },
  // 新增：手动收起功能面板的方法
  collapseFeatures: () => {
    isExpanded.value = false
    emit('expand-change', false)
  },
})
</script>

<style lang="less" scoped>
.chat-bottom-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #1f0038;
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 桌面端适配 - 与PC端chat容器宽度保持一致 */
  @media (min-width: 768px) {
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: calc(calc(var(--vh, 1vh) * 100) * 9 / 16);
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);

    /* 确保在极端情况下有最小和最大宽度限制 */
    min-width: 375px;

    /* 在超宽屏幕上限制最大宽度 */
    @media (min-height: 1200px) {
      max-width: 675px; /* 1200 * 9 / 16 = 675px */
    }
  }

  /* 支持不支持aspect-ratio的浏览器 */
  @supports not (aspect-ratio: 9/16) {
    @media (min-width: 768px) {
      width: calc(calc(var(--vh, 1vh) * 100) * 9 / 16);
      max-width: calc(calc(var(--vh, 1vh) * 100) * 9 / 16);
    }
  }
}

.input-section {
  padding: 8px 16px;
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);

  /* 桌面端适配 */
  @media (min-width: 768px) {
    padding: 12px 24px;
  }
}

.input-container {
  display: flex;
  align-items: center;
  gap: 12px;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    gap: 16px;
  }
}

.input-field {
  flex: 1;
  display: flex;
  align-items: center;
  background: #4c3c59;
  border-radius: 40px;
  padding: 6px 8px 6px 16px;
  height: 36px;
  gap: 10px;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    height: 44px;
    padding: 8px 12px 8px 20px;
    gap: 12px;
    border-radius: 22px;
  }
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* 桌面端适配 */
  @media (min-width: 768px) {
    font-size: 16px;
  }
}

.send-button {
  width: 24px;
  height: 24px;
  border-radius: 12.57px;
  background: #ca93f2;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.5;
  transition: all 0.2s ease;

  &.active {
    opacity: 1;
  }

  &:disabled {
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    transform: scale(1.05);
    opacity: 1;
  }

  .send-icon {
    width: 14.86px;
    height: 14.86px;
  }

  /* 桌面端适配 */
  @media (min-width: 768px) {
    width: 28px;
    height: 28px;
    border-radius: 14px;
  }
}

.expand-button {
  width: 24px;
  height: 24px;
  // border: 1.5px solid #ffffff;
  border-radius: 40px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  .expand-icon,
  .collapse-icon {
    width: 100%;
    height: 100%;
  }

  /* 桌面端适配 */
  @media (min-width: 768px) {
    width: 28px;
    height: 28px;
    border-radius: 14px;
  }
}

.features-section {
  padding: 12px 16px;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    padding: 16px 24px 20px;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px 26px; /* 行间距16px，列间距26px */
  justify-items: center;

  /* 桌面端：单行7列布局 */
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 0 30px;
    justify-content: center;
  }
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &:hover {
    transform: translateY(-2px);

    .feature-icon {
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .feature-text {
      color: rgba(255, 255, 255, 0.9);
    }
  }

  /* 桌面端适配 */
  @media (min-width: 768px) {
    gap: 6px;
  }
}

.feature-icon {
  width: 52px;
  height: 52px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10.4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;

  .svg-icon {
    fill: currentColor;
  }

  /* 桌面端适配 */
  @media (min-width: 768px) {
    width: 60px;
    height: 60px;
    border-radius: 12px;
  }
}

.feature-text {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  transition: color 0.2s ease;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    font-size: 13px;
  }
}

/* Live streaming icon */
.live-icon {
  .live-content {
    width: 36px;
    height: 36px;
    position: relative;

    .live-box {
      width: 26px;
      height: 24px;
      background: #ca92f2;
      border-radius: 2.25px;
      position: absolute;
      top: 4.08px;
      left: 5px;
    }

    .live-triangle {
      width: 11.25px;
      height: 11.25px;
      background: #8bd5b4;
      clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
      position: absolute;
      top: 8.45px;
      left: 12.37px;
    }

    .live-dots {
      position: absolute;
      top: 0;
      left: 11.25px;

      .dot {
        width: 5.23px;
        height: 7.04px;
        background: #8bd5b4;
        border-radius: 1.125px;
        position: absolute;

        &:first-child {
          left: 0;
        }

        &:last-child {
          left: 8.27px;
        }
      }
    }
  }
}

/* Video call icon */
.video-icon {
  .video-content {
    width: 36px;
    height: 36px;
    position: relative;

    .video-camera {
      width: 22.67px;
      height: 28px;
      background: #8782ff;
      border-radius: 3px;
      position: absolute;
      top: 4px;
      left: 6.67px;

      .camera-lens {
        width: 8px;
        height: 1px;
        background: #cbffc8;
        position: absolute;
        top: 8px;
        left: 7.33px;
      }
    }

    .person-icon {
      position: absolute;
      top: 12px;
      left: 12.33px;

      .person-head {
        width: 6px;
        height: 6px;
        background: #8782ff;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 2px;
      }

      .person-body {
        width: 10px;
        height: 5px;
        background: #8782ff;
        border-radius: 0 0 0 0;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
  }
}

/* Gift box icon */
.gift-icon {
  .gift-content {
    width: 36px;
    height: 36px;
    position: relative;

    .gift-bow {
      position: absolute;
      top: 2.42px;
      left: 7px;

      .bow-left {
        width: 12.15px;
        height: 11.07px;
        background: #fa7665;
        border-radius: 4.3125px;
        position: absolute;
        top: 0;
        left: 0;
      }

      .bow-right {
        width: 12.15px;
        height: 11.07px;
        background: #fa7665;
        border-radius: 4.3125px;
        position: absolute;
        top: 0;
        left: 8.85px;
      }
    }

    .gift-box-top {
      width: 25px;
      height: 10px;
      background: #fa7665;
      position: absolute;
      top: 7.58px;
      left: 5px;
    }

    .gift-box-body {
      width: 23px;
      height: 14.52px;
      background: #fa7665;
      position: absolute;
      top: 18.06px;
      left: 6px;
    }
  }
}

/* Monitor icon */
.monitor-icon {
  .monitor-content {
    width: 36px;
    height: 36px;
    position: relative;

    .monitor-screen {
      position: absolute;
      top: 3px;
      left: 8px;

      .screen-outer {
        width: 20px;
        height: 22.28px;
        background: #89adff;
        position: absolute;
        top: 0;
        left: 0;
      }

      .screen-inner {
        width: 8px;
        height: 8px;
        background: #35194c;
        border-radius: 50%;
        position: absolute;
        top: 4.5px;
        left: 6px;
      }
    }

    .monitor-base {
      width: 27px;
      height: 7px;
      background: #89adff;
      position: absolute;
      top: 25px;
      left: 5px;
    }
  }
}

/* 解锁等级标记样式 */
.unlock-level-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ff4757;
  color: #ffffff;
  font-size: 8px;
  font-weight: 600;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 6px;
  min-width: 16px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-badge {
  position: absolute;
  top: -4px;
  right: -8px;
  background: linear-gradient(135deg, #daff96 0%, #b8e66d 100%);
  color: #1a1225;
  font-size: 8px;
  font-weight: 700;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 6px;
  min-width: 18px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  box-shadow: 0 2px 4px rgba(218, 255, 150, 0.3);
  animation: pulse 2s infinite;

  .diamond-icon {
    width: 8px;
    height: 8px;
    object-fit: contain;
  }
}

.free-badge {
  position: absolute;
  top: -4px;
  right: -8px;
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  color: #ffffff;
  font-size: 7px;
  font-weight: 700;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 6px;
  min-width: 18px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 210, 255, 0.3);
  animation: glow 2s infinite alternate;
}

@keyframes glow {
  0% {
    box-shadow: 0 2px 4px rgba(0, 210, 255, 0.3);
  }
  100% {
    box-shadow: 0 2px 8px rgba(0, 210, 255, 0.6);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

/* 解锁等级标记动画 */
@keyframes pulse-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* SVG图标样式 */
.meetup-icon {
  .meetup-svg-1,
  .meetup-svg-2 {
    position: absolute;
    fill: #ff85e2;
  }

  .meetup-svg-1 {
    width: 13.5px;
    height: 27px;
    top: 4.5px;
    left: 2.84px;
  }

  .meetup-svg-2 {
    width: 12.38px;
    height: 24.75px;
    top: 5.63px;
    left: 18.59px;
  }
}

.dance-icon {
  .dance-svg-1,
  .dance-svg-2 {
    position: absolute;
    fill: #ff80a6;
  }

  .dance-svg-1 {
    width: 11.76px;
    height: 29px;
    top: 3.59px;
    left: 5.53px;
  }

  .dance-svg-2 {
    width: 13px;
    height: 30px;
    top: 2.59px;
    left: 18.64px;
  }
}

.concert-icon {
  .concert-svg-1,
  .concert-svg-2,
  .concert-svg-3,
  .concert-svg-4,
  .concert-svg-5 {
    position: absolute;
    fill: #fff08c;
  }

  .concert-svg-1 {
    width: 14.14px;
    height: 13.1px;
    top: 2.84px;
    left: 18px;
  }

  .concert-svg-2 {
    width: 26px;
    height: 22px;
    top: 10px;
    left: 5px;
  }

  .concert-svg-3 {
    width: 5px;
    height: 5px;
    top: 13px;
    left: 17px;
  }

  .concert-svg-4 {
    width: 2px;
    height: 2px;
    top: 29px;
    left: 8px;
  }

  .concert-svg-5 {
    width: 3px;
    height: 3px;
    top: 27px;
    left: 8px;
  }
}

.safe-area-bottom {
  background: #f5f5f5;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 13px;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    height: 20px;
    padding-bottom: 8px;
    background: rgba(245, 245, 245, 0.8);
  }

  /* 大屏幕桌面端隐藏 */
  @media (min-width: 1024px) {
    display: none;
  }
}

.home-indicator {
  width: 134px;
  height: 5px;
  background: #ffffff;
  border-radius: 100px;

  /* 桌面端适配 */
  @media (min-width: 768px) {
    width: 100px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
  }
}

// 动画
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 注意：所有旧的CSS图标样式（live-icon, video-icon, gift-icon等）已被SVG图标替换 */
</style>
