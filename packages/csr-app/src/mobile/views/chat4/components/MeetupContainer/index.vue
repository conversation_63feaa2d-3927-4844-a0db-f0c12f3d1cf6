<template>
  <div class="meetup-container">
    <!-- 顶部导航栏 -->
    <div class="meetup-header">
      <div class="header-left">
        <!-- 返回按钮 -->
        <button class="back-button" @click="handleBack">
          <span class="back-icon"><icon-left /></span>
        </button>

        <!-- 角色信息 -->
        <div class="character-info">
          <div class="character-name">{{ characterName }}</div>
        </div>
      </div>

      <!-- <div class="header-right">
        <button class="map-button" @click="handleShowMap">
          <img src="@/assets/icon/map-icon.svg" alt="Map" class="map-icon" />
        </button>
      </div> -->
    </div>

    <!-- 聊天背景 -->
    <EnhancedChatContainer
      :is-playing-video="false"
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="backgroundImage"
      class="background-container"
      @unlock-blur="handleUnlockBlur"
    >
      <template #chat-interface>
        <!-- 好感度系统显示 -->
        <div class="stage-container">
          <Stage
            :favorability-state="chatEventsStore.favorabilityState"
            @click="handleStageClick"
          />
        </div>

        <!-- 金币显示 -->
        <div class="coin-display">
          <slot name="coin-display"></slot>
        </div>

        <!-- 角色对话区域 -->
        <div v-if="shouldShowAnyMessage" class="character-dialogue">
          <!-- 用户消息 -->
          <div v-if="userMessage" class="user-message-bubble">
            {{ userMessage }}
          </div>

          <!-- 角色消息和系统消息 -->
          <div v-else-if="shouldShowMessage">
            <!-- 系统消息样式 -->
            <div v-if="isSystemMessage" class="system-message-bubble">
              {{ displayMessage }}
            </div>

            <!-- 角色消息样式 -->
            <div v-else class="character-message-bubble">
              <div class="character-avatar">
                <img :src="characterAvatar" :alt="characterName" />
              </div>
              <div class="message-text">
                <span v-if="isActorThinking" class="thinking-text">
                  {{ characterName }} is thinking...
                </span>
                <span v-else>{{ displayMessage }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Chat4 选项卡 -->
        <Chat4Options @select="handleChatOptionSelect" />

        <!-- Continue 按钮 -->
        <div v-if="!showChatOptions" class="continue-button-container">
          <button
            class="continue-button"
            :disabled="isContinueDisabled"
            @click="handleMeetupContinue"
          >
            {{ continueButtonText }}
          </button>
        </div>

        <!-- 底部行为区域 (暂时取消)
        <div v-if="!showChatOptions" class="action-area">
          <div class="action-buttons">
            <button class="action-btn back-btn" @click="$emit('back-click')">
              <div class="btn-icon">
                <span class="back-icon"><icon-left></icon-left></span>
              </div>
            </button>

            <button class="action-btn chat-btn" @click="handleChatClick">
              <div class="btn-icon">
                <component :is="getActionIcon('chat')" />
              </div>
              <div class="btn-label">
                <span class="btn-text">Chat</span>
              </div>
            </button>

            <button
              v-for="action in sceneActions.filter((a) => a.key !== 'chat')"
              :key="action.key"
              class="action-btn"
              :class="`${action.key}-btn`"
              @click="handleActionClick(action)"
            >
              <div class="btn-icon">
                <img
                  v-if="action.imageUrl"
                  :src="action.imageUrl"
                  :alt="action.label"
                  class="action-icon-image"
                />
                <component v-else :is="getActionIcon(action.icon)" />
              </div>
              <div class="btn-label">
                <span class="btn-text">{{ action.label }}</span>
                <div
                  v-if="action.requirement && !isActionUnlocked(action)"
                  class="unlock-level-badge"
                >
                  {{ getActionRequiredLevelText(action) }}
                </div>
                <div v-else-if="action.requirement" class="level-badge">
                  {{ getActionRequiredLevelText(action) }}
                </div>
              </div>
            </button>
          </div>
        </div>
        -->
      </template>
    </EnhancedChatContainer>

    <!-- 聊天输入框 -->
    <div
      v-if="showChatInput"
      class="chat-input-overlay"
      @click="closeChatInput"
    >
      <div class="chat-input-container" @click.stop>
        <div class="input-row">
          <div class="input-field">
            <input
              v-model="chatMessage"
              placeholder="Chat here"
              class="message-input"
              @keydown.enter.prevent="sendChatMessage"
              ref="messageInputRef"
            />
          </div>

          <button
            class="send-btn"
            @click="sendChatMessage"
            :disabled="!chatMessage.trim()"
          >
            <svg width="19" height="19" viewBox="0 0 19 19" fill="none">
              <path
                d="M2.5 9.5L16.5 2.5L9.5 16.5L7.5 9.5L2.5 9.5Z"
                fill="white"
                stroke="white"
                stroke-width="1.5"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 解锁弹窗 -->
    <SceneUnlockModal
      :visible="showUnlockModal"
      :scene-name="lockedActionName"
      :required-level="lockedActionRequiredLevel"
      :required-coins="lockedActionRequiredCoins"
      :required-heart-value="lockedActionRequiredHeartValue"
      :current-level="currentFavorabilityLevel"
      :current-heart-value="currentHeartValue"
      @close="handleUnlockModalClose"
      @boost-favorability="handleBoostFavorability"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useUserStore } from '@/store/user'
import { useChat4Store } from '@/store/chat4'
import EnhancedChatContainer from '@/mobile/components/EnhancedChatContainer.vue'

import MeetupKissIcon from '@/assets/icon/meetup-kiss-icon.svg'
import MeetupTouchIcon from '@/assets/icon/meetup-touch-icon.svg'
import MeetupHugIcon from '@/assets/icon/meetup-hug-icon.svg'
import MeetupChatIcon from '@/assets/icon/meetup-chat-icon.svg'
import SceneUnlockModal from '../SceneUnlockModal/index.vue'
import Stage from '@/mobile/views/chat4/components/Stage/index.vue'
import Chat4Options from '../Chat4Options/index.vue'
import IconLeft from '@arco-design/web-vue/es/icon/icon-left'

import { useSceneUnlock } from '../../composables/useSceneUnlock'
import { useIconConfig } from '@/composables/useIconConfig'
import { useChat4StateMachine } from '@/composables/useChat4StateMachine'

import { SceneUnlockUtils } from '@/types/favorability'

interface Props {
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  backgroundVideo?: string
  messages?: any[] // 添加消息数组 prop
  currentScene?: string
}

interface Emits {
  (e: 'back-click'): void
  (e: 'send-message', message: string): void
  (e: 'open-gift-modal'): void
  (e: 'stage-click'): void
  (e: 'show-auth-drawer'): void
  (e: 'back'): void
  (e: 'show-map'): void
  (e: 'add-coins'): void
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Tsunade',
  characterAvatar: '',
  backgroundImage: '',
  backgroundVideo: '',
  messages: () => [],
})

const emit = defineEmits<Emits>()

// Store
const chatMessagesStore = useChatMessagesStore()
const chatEventsStore = useChatEventsStore()
const chatResourcesStore = useChatResourcesStore()
const userStore = useUserStore()
const chat4Store = useChat4Store()

// 图标配置
const { icons } = useIconConfig()

// 状态机
const stateMachine = useChat4StateMachine()

// 解锁状态管理
const sceneUnlock = useSceneUnlock()

// 解锁弹窗状态
const showUnlockModal = ref(false)
const lockedActionName = ref('')
const lockedActionRequiredLevel = ref('')
const lockedActionRequiredCoins = ref(0)
const lockedActionRequiredHeartValue = ref(0)

// 选项卡相关状态
const showChatOptions = computed(() => {
  return chatResourcesStore.chat4Card?.length
})

// 状态
const showChatInput = ref(false)
const chatMessage = ref('')
const messageInputRef = ref<HTMLInputElement>()
const displayMessage = ref('')
const userMessage = ref('')
const messageTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const userMessageTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const currentMessageType = ref<string>('')

// 计算属性
const isActorThinking = computed(() => chatMessagesStore.isActorThinking)

const shouldShowMessage = computed(() => {
  return displayMessage.value || isActorThinking.value
})

const shouldShowAnyMessage = computed(() => {
  // 优先显示用户消息，如果没有用户消息则显示角色消息
  return userMessage.value || shouldShowMessage.value
})

const isSystemMessage = computed(() => {
  return currentMessageType.value === 'system'
})

// 顶部导航栏数据
const intimacyValue = computed(() => {
  // 从好感度状态获取当前心值，默认为80
  return chatEventsStore.favorabilityState?.currentHeartValue || 80
})

const userCoins = computed(() => {
  return userStore.userInfo?.coins || 0
})

// Continue按钮状态管理
const isProcessing = computed(() => {
  return chatEventsStore.isProcessingQueue
})

const isContinueDisabled = computed(() => {
  // 如果正在处理队列，禁用按钮
  console.log('isProcessing.value:', isProcessing.value)
  if (isProcessing.value) return true

  // 如果有选项卡显示，禁用按钮
  if (showChatOptions.value) return true

  // 如果没有下一个可处理的事件，禁用按钮
  return !chatEventsStore.hasNextProcessableEvent()
})

// 场景名称映射
const sceneNameMap: Record<string, string> = {
  Map: 'Map',
  Living: 'Living Room',
  Phone: 'Chat Room',
  MeetupPool: 'Pool',
  MeetupCoffee: 'Coffee Shop',
  MeetupOffice: 'Office',
  MeetupSeaside: 'Seaside',
  VideoCall: 'Video Call',
  Dancing: 'Dancing',
  Concert: 'Concert',
  Diary: 'Diary',
  Game: 'Game',
}

// 检测是否有pending的目标场景
const hasPendingTargetScene = computed(() => {
  return !!chatEventsStore.pendingTargetScene
})

// 获取目标场景的友好名称
const targetSceneName = computed(() => {
  if (!chatEventsStore.pendingTargetScene) return ''
  const targetSceneId = chatEventsStore.pendingTargetScene
  return sceneNameMap[targetSceneId] || targetSceneId
})

// Continue按钮的文本
const continueButtonText = computed(() => {
  if (isProcessing.value) return 'Processing...'
  if (hasPendingTargetScene.value) {
    return `Back to ${targetSceneName.value}`
  }
  if (showChatOptions.value) {
    return 'Choose Option'
  }
  if (chatEventsStore.hasNextProcessableEvent()) {
    return 'Continue'
  }
  return 'Continue'
})

const latestActorMessage = computed(() => {
  const messages = chatMessagesStore.messages
  if (!messages || messages.length === 0) {
    return "Dad I'll do anything you want as long"
  }

  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    if (message.sender_type === 'actor' && message.content?.text) {
      return message.content.text
    }
  }

  return "Dad I'll do anything you want as long"
})

// 当前用户状态 - 从实际的好感度系统获取
const currentFavorabilityLevel = computed(() => {
  const currentLevel = chatEventsStore.favorabilityState.currentLevel
  // 使用SceneUnlockUtils格式化等级显示，level0 显示为 Lv0
  return currentLevel ? SceneUnlockUtils.formatLevelText(currentLevel) : 'Lv0'
})

const currentHeartValue = computed(() => {
  return chatEventsStore.favorabilityState.currentHeartValue || 0
})

const sceneActions = computed(() => {
  return chat4Store.sceneActionsMenu || []
})

// 解锁检查方法
const isActionUnlocked = (action: any) => {
  // 如果没有解锁要求，默认解锁
  if (!action.requirement || !action.value) return true

  if (action.requirement === 'heart_value') {
    // 检查心值要求
    const currentHeartValue =
      chatEventsStore.favorabilityState.currentHeartValue || 0
    return currentHeartValue >= action.value
  }

  // 其他类型的要求可以在这里扩展
  return true
}

const getActionRequiredLevelText = (action: any) => {
  if (!action.requirement || !action.value) return ''

  if (action.requirement === 'heart_value') {
    // 将心值要求转换为等级显示
    const { levelInfos } = chatEventsStore.favorabilityState

    // 找到满足心值要求的最低等级
    let requiredLevel = null
    for (const levelInfo of levelInfos) {
      if (levelInfo.heart_value >= action.value) {
        requiredLevel = levelInfo.level
        break
      }
    }

    return requiredLevel
      ? SceneUnlockUtils.formatLevelText(requiredLevel)
      : `${action.value}♥`
  }

  return ''
}

// 方法
const getActionIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    kiss: MeetupKissIcon,
    touch: MeetupTouchIcon,
    hug: MeetupHugIcon,
    chat: MeetupChatIcon,
  }
  return iconMap[iconName] || MeetupChatIcon
}

const handleActionClick = async (action: any) => {
  console.log('Action clicked:', action)

  // 检查动作是否解锁
  if (!isActionUnlocked(action)) {
    console.log(`Action ${action.key} is locked, showing unlock modal`)

    showUnlockModal.value = true
    lockedActionName.value = action.label || action.key
    lockedActionRequiredLevel.value = getActionRequiredLevelText(action)
    lockedActionRequiredCoins.value = 0 // meetup 动作通常不需要金币
    lockedActionRequiredHeartValue.value =
      action.requirement === 'heart_value' ? action.value : 0
    return
  }

  try {
    // 调用 sendMessage 发送对应按钮的 key 值
    await chatEventsStore.sendMessage(
      action.key,
      null,
      'text',
      false,
      null,
      0,
      false,
      {},
      props.currentScene,
    )
  } catch (error) {
    console.error('Failed to send action message:', error)
  }
}

// 处理选项卡选择 - 类似chat2的handleTelepathyComplete逻辑
const handleChatOptionSelect = async (option: any) => {
  console.log('Chat4 option selected:', option)

  try {
    if (option.scene_id) {
      await chatEventsStore.sendMessage(
        option.text,
        option.option_id,
        'text',
        true,
        option.scene_id,
      )
    } else {
      await chatEventsStore.sendMessage(option.text, option.option_id)
    }
    chatResourcesStore.chat4Card = null
  } catch (error) {
    console.error('Failed to send chat option message:', error)
  }
}

const handleMeetupContinue = async () => {
  // 如果有pending的目标场景，直接使用状态机跳转
  if (hasPendingTargetScene.value) {
    const targetSceneId = chatEventsStore.pendingTargetScene
    if (targetSceneId) {
      // 清除pending状态
      chatEventsStore.clearPendingTargetScene()
      // 使用状态机跳转到目标场景
      await stateMachine.performServerSceneJump(targetSceneId, true)
    }
    return
  }

  // 如果有选项卡，选项卡会自动显示，不需要处理
  if (showChatOptions.value) {
    return
  }

  // 如果队列中有下一个可处理的事件，处理下一个事件
  if (chatEventsStore.hasNextProcessableEvent()) {
    await chatEventsStore.processNextEvent()
    return
  }

  // 如果没有事件可处理，发送空消息继续对话
  await chatEventsStore.sendMessage('', null, 'text', true, null, 0)
}

const handleChatClick = () => {
  showChatInput.value = true
  // 下一帧聚焦输入框
  nextTick(() => {
    messageInputRef.value?.focus()
  })
}

// 顶部导航栏事件处理
const handleBack = async () => {
  // TODO: 临时解决方案 - 强制跳转到地图场景
  // 原因：状态机状态同步存在问题，导致guard检查失败
  // 需要后续深入排查SERVER_SCENE_CHANGE转换失败的根本原因

  try {
    // 临时方案：直接调用performServerSceneJump强制跳转到地图
    // 这样可以绕过guard检查，确保功能正常工作
    await stateMachine.performServerSceneJump('Map', true)
  } catch (error) {
    // 如果强制跳转也失败，尝试原来的方法
    try {
      await stateMachine.goToMap()
    } catch (fallbackError) {
      // 静默处理回退失败
    }
  }
}

const handleShowMap = async () => {
  // 返回地图 - 直接使用状态机跳转
  await stateMachine.goToMap()
}

const handleAddCoins = () => {
  // 添加金币 - 可能打开充值页面
  emit('add-coins')
}

const closeChatInput = () => {
  showChatInput.value = false
  chatMessage.value = ''
}

const sendChatMessage = async () => {
  if (!chatMessage.value.trim()) return

  try {
    emit('send-message', chatMessage.value)
    closeChatInput()
  } catch (error) {
    console.error('Failed to send chat message:', error)
  }
}

// 解锁弹窗事件处理
const handleUnlockModalClose = () => {
  showUnlockModal.value = false
  lockedActionName.value = ''
  lockedActionRequiredLevel.value = ''
  lockedActionRequiredCoins.value = 0
  lockedActionRequiredHeartValue.value = 0
}

const handleBoostFavorability = () => {
  console.log('Boost favorability clicked in MeetupContainer')
  handleUnlockModalClose()
  // 触发打开礼物弹窗
  emit('open-gift-modal')
}

// Stage 点击处理
const handleStageClick = () => {
  console.log('Stage clicked in MeetupContainer, emitting to parent')
  emit('stage-click')
}

// 解锁模糊处理
const handleUnlockBlur = (tag: string, requiredHeartValue: number) => {
  console.log('Unlock blur requested in MeetupContainer:', {
    tag,
    requiredHeartValue,
  })
  chat4Store.requestUnlockBlur(tag, requiredHeartValue)
}

// 监听消息变化 - 主要监听chatMessagesStore.messages (像VideoCallContainer一样)
watch(
  () => chatMessagesStore.messages,
  (newMessages, oldMessages) => {
    if (newMessages.length > 0) {
      const latestMessage = newMessages[newMessages.length - 1]

      if (latestMessage.sender_type === 'user' && latestMessage.content?.text) {
        // 处理用户消息
        if (userMessageTimer.value) {
          clearTimeout(userMessageTimer.value)
        }

        // 立即显示用户消息
        userMessage.value = latestMessage.content.text

        // 3秒后隐藏用户消息
        userMessageTimer.value = setTimeout(() => {
          userMessage.value = ''
        }, 3000)
      } else if (
        (latestMessage.sender_type === 'actor' ||
          latestMessage.sender_type === 'system') &&
        latestMessage.content?.text
      ) {
        // 处理角色消息和系统消息
        if (messageTimer.value) {
          clearTimeout(messageTimer.value)
        }

        // 立即显示消息，不自动隐藏
        displayMessage.value = latestMessage.content.text
        currentMessageType.value = latestMessage.sender_type
      }
    }
  },
  { deep: true },
)

// 备用监听props.messages变化 (兼容性)
watch(
  () => props.messages,
  (newMessages) => {
    if (newMessages && newMessages.length > 0) {
      const latestMessage = newMessages[newMessages.length - 1]

      // 适配 displayMessages 的格式：使用 type 而不是 sender_type，content 是字符串而不是对象
      const messageType = latestMessage.sender_type || latestMessage.type
      const messageText = latestMessage.content?.text || latestMessage.content

      // 只有在chatMessagesStore.messages为空时才使用props.messages
      if (chatMessagesStore.messages.length === 0) {
        if (messageType === 'user' && messageText) {
          if (userMessageTimer.value) {
            clearTimeout(userMessageTimer.value)
          }
          userMessage.value = messageText
          userMessageTimer.value = setTimeout(() => {
            userMessage.value = ''
          }, 3000)
        } else if (
          (messageType === 'actor' || messageType === 'system') &&
          messageText
        ) {
          if (messageTimer.value) {
            clearTimeout(messageTimer.value)
          }
          displayMessage.value = messageText
          currentMessageType.value = messageType
        }
      }
    }
  },
  { immediate: true },
)

// 监听isActorThinking状态变化
watch(
  () => chatMessagesStore.isActorThinking,
  (newValue, oldValue) => {
    if (oldValue === true && newValue === false) {
      // 当thinking结束时，确保有消息显示
      if (!displayMessage.value && chatMessagesStore.messages.length > 0) {
        const latestMessage =
          chatMessagesStore.messages[chatMessagesStore.messages.length - 1]
        if (
          (latestMessage.sender_type === 'actor' ||
            latestMessage.sender_type === 'system') &&
          latestMessage.content?.text
        ) {
          displayMessage.value = latestMessage.content.text
          currentMessageType.value = latestMessage.sender_type
        }
      }
    }
  },
)

onBeforeUnmount(() => {
  // 清理定时器
  if (messageTimer.value) {
    clearTimeout(messageTimer.value)
  }

  if (userMessageTimer.value) {
    clearTimeout(userMessageTimer.value)
  }
})
</script>

<style lang="less" scoped>
.meetup-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
}

/* 顶部导航栏样式 */
.meetup-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: transparent;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-button {
  width: 36px;
  height: 36px;
  border-radius: 22.5px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20.45px);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.back-icon {
  font-weight: 400;
  line-height: 1.19;
  color: #ffffff;
  text-align: center;
  svg {
    color: #ffffff;
  }
}

.character-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.4;
}

.intimacy-display {
  position: relative;
  width: 44px;
  height: 19px;
}

.intimacy-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 36px;
  height: 11px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #ff6060 50%,
    transparent 100%
  );
  border-radius: 128px;
  opacity: 0.59;
}

.heart-icon {
  width: 8px;
  height: 7px;
}

.intimacy-number {
  color: #ffffff;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.4;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.coin-display-header {
  display: flex;
  align-items: center;
  background: #ca93f2;
  border-radius: 0px 34px 34px 0px;
  padding: 0;
  height: 24px;
  position: relative;
}

.diamond-icon {
  width: 28px;
  height: 28px;
  margin-left: -14px;
  margin-top: -2px;
}

.coin-amount {
  color: #1f0038;
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 13px;
  line-height: 1.17;
  padding: 0 8px;
}

.add-coin-button {
  background: none;
  border: none;
  color: #1f0038;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.map-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-icon {
  width: 18px;
  height: 18px;
  color: #ffffff;
}

/* Stage 好感度系统区域 */
.stage-container {
  position: absolute;
  top: 60px; /* 与聊天消息区域对齐 */
  right: 0px; /* 右侧边距 */
  z-index: 4; /* 在聊天消息之上 */
  pointer-events: auto; /* 确保可以点击 */
}

.background-container {
  width: 100%;
  height: 100%;
}

/* 金币显示 */
.coin-display {
  position: absolute;
  top: 20px;
  right: 10px;
  z-index: 10;
  pointer-events: auto;
}

/* 角色对话区域 */
.character-dialogue {
  position: absolute;
  bottom: 150px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 335px;
  max-width: calc(100vw - 40px);
}

/* 用户消息样式 */
.user-message-bubble {
  background: rgba(202, 147, 242, 0.9);
  color: white;
  padding: 14px 16px;
  border-radius: 48px;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
  text-align: center;
  backdrop-filter: blur(8px);
}

/* 角色消息样式 - 按照Figma设计 */
.character-message-bubble {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 16px;
  background: rgba(31, 0, 56, 0.5);
  border-radius: 48px;
  backdrop-filter: blur(8px);

  .character-avatar {
    width: 36px;
    height: 36px;
    border-radius: 80px;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-text {
    flex: 1;
    color: #ffffff;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    max-width: 243px;

    .thinking-text {
      font-style: italic;
      opacity: 0.8;
    }
  }
}

/* 系统消息样式 - 按照Figma设计 */
.system-message-bubble {
  width: 335px;
  max-width: calc(100vw - 40px);
  min-height: 88px;
  padding: 14px 32px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 48px;
  backdrop-filter: blur(16px);

  color: #000000;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
  text-align: justify;

  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部行为区域 */
.action-area {
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  border-top: 1px solid #1f0038;
  z-index: 10;
}

.action-buttons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 7px 42px 0 0;

  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: none;
    background: none;
    cursor: pointer;
    position: relative;

    .btn-icon {
      width: 50px;
      height: 50px;
      border-radius: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 2px 2px 0px 0px rgba(115, 99, 166, 0.5);
      margin-bottom: 2px;
      overflow: hidden; /* 确保图片不会溢出圆形边界 */

      .back-icon {
        color: #4c3c59;
        font-family: 'SF Pro', sans-serif;
        font-size: 24px;
        font-weight: 400;
      }

      .action-icon-image {
        width: 100%;
        height: 100%;
        object-fit: cover; /* 保持图片比例并填充整个圆形区域 */
        border-radius: 26px; /* 与父容器的 border-radius 保持一致 */
      }

      svg {
        color: #4c3c59;
      }
    }

    .btn-label {
      position: relative;
      height: 22px;
      border: none;
      border-radius: 1.4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #ffdbd3 0%, #ffb762 100%);
      box-shadow:
        0px 0px 0px 2px #ffffff,
        0px 0px 0px 3px #1f0038;
      width: 50px;

      .btn-text {
        color: #1f0038;
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 8px;
        line-height: 1.17;
        text-align: center;
        padding: 6px 16px;
      }

      .level-badge {
        position: absolute;
        top: -8px;
        right: -12px;
        background: linear-gradient(180deg, #35f7ff 0%, #814bff 100%);
        border: 1px solid #ffffff;
        border-radius: 430px;
        padding: 0px 4px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        color: #daff96;
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 8px;
        line-height: 1.17;
      }

      /* 解锁等级标记样式 */
      .unlock-level-badge {
        position: absolute;
        top: -8px;
        right: -12px;
        background: linear-gradient(180deg, #ff4757 0%, #ff3742 100%);
        border: 1px solid #ffffff;
        border-radius: 430px;
        padding: 0px 4px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        color: #ffffff;
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 8px;
        line-height: 1.17;
        box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
      }
    }
  }

  .back-btn {
    .btn-icon {
      background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
      border-radius: 0px 40px 40px 0px;
      width: 42px;

      .back-icon {
        // background: rgba(255, 255, 255, 0.8);
        border-radius: 22.5px;
        // backdrop-filter: blur(20px);
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .kiss-btn,
  .touch-btn,
  .hug-btn,
  .chat-btn {
    .btn-icon {
      background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
    }
  }
}

/* 聊天输入框 */
.chat-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-end;
  z-index: 100;
}

.chat-input-container {
  width: 100%;
  background: #f6f6f6;
  padding: 8px 15px;
  height: 52px;
  display: flex;
  align-items: center;

  .input-row {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;

    .input-field {
      flex: 1;
      background: #4c3c59;
      border-radius: 20px;
      box-shadow: 0px 0px 10px 0px rgba(218, 255, 150, 0.15);
      padding: 0px 3px 0px 15px;
      height: 36px;
      display: flex;
      align-items: center;

      .message-input {
        flex: 1;
        background: transparent;
        border: none;
        outline: none;
        color: #ffffff;
        font-family: 'Work Sans', sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.17;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .send-btn {
      width: 36px;
      height: 36px;
      background: #ca93f2;
      border: none;
      border-radius: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.5;
      transition: opacity 0.3s ease;

      &:not(:disabled) {
        opacity: 1;
      }

      &:disabled {
        cursor: not-allowed;
      }

      svg {
        width: 18.57px;
        height: 18.57px;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Continue 按钮样式 */
.continue-button-container {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.continue-button {
  padding: 12px 32px;
  border-radius: 25px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
  min-width: 120px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);
  }

  &:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    border-color: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 6px rgba(156, 163, 175, 0.2);
  }

  &:focus {
    outline: none;
    box-shadow:
      0 4px 15px rgba(99, 102, 241, 0.3),
      0 0 0 3px rgba(99, 102, 241, 0.2);
  }
}
</style>
