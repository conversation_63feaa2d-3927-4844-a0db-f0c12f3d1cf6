import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useStorage } from '@vueuse/core'
import { BaseSSEData } from './chat-types'
import {
  startChatSSE,
  sendMessageSSE,
  jumpToPreviousSceneSSE,
  jumpToNextSceneSSE,
} from '@/services/chat'
import type {
  ShowScenesUnlockConditionsEventData,
  ShowLevelInfoEventData,
  FavorabilityState,
  SceneUnlockStatus,
} from '@/types/favorability'
import { SceneUnlockUtils } from '@/types/favorability'
import { JSONParse } from '@/utils'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { useSkillStore } from '@/store/skill'
import { useRechargeStore } from '@/store/recharge'
import { useChatMessagesStore } from './chat-messages'
import { useChatResourcesStore } from './chat-resources'
import { useChatUIStore } from './chat-ui'
import { useChatTasksStore } from './chat-tasks'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { Message } from '@/mobile/components/Message'
import { v4 as uuidv4 } from 'uuid'
import {
  getCurrentPlaytime,
  resetPlaytime,
  getCurrentHeartValue,
} from '@/api/game'
import {
  SceneType,
  SceneActionType,
  SceneUtils,
  type SceneCustomEventDetail,
} from '@/types/chat4-scene'

export const useChatEventsStore = defineStore('chatEvents', () => {
  // 状态
  const currentActorId = ref<string | null>(null)
  const conversationId = ref<string | null>(null)
  const error = ref<string | null>(null)
  const messageQueue = ref<BaseSSEData[]>([])
  const isProcessingQueue = ref(false)
  const lastSceneTimestamp = ref<number | null>(null)
  const lastScene = ref<string | null>(null)
  const currentScene = ref<string | null>(null) // 当前场景ID，用于Chat4等需要场景信息的功能
  const recordedSceneId = ref<string | null>(null) // 从handle_scene_change事件记录的场景ID
  const countdownInterval = ref<ReturnType<typeof setInterval> | null>(null)
  const timeoutId = ref<ReturnType<typeof setTimeout> | null>(null)
  const isShouldRestart = useStorage<boolean>('chat-is-should-restart', true)
  const paymentRequired = ref(false)
  const inPaidScene = ref(false)
  const paidSceneInterval = ref<ReturnType<typeof setInterval> | null>(null)
  const billingInterval = ref<number | null>(null) // 付费场景计费间隔（多少秒扣一次费），单位秒
  const isConfirmedLeave = ref(false) // 是否已确认离开游戏（用于钻石用完模态框）
  const showPaidSceneDialog = ref(false) // 是否显示付费场景提醒对话框

  // Pending target scene for meetup
  const pendingTargetScene = ref<string | null>(null)

  // Getters
  const hasPendingEvents = computed(
    () => messageQueue.value.length > 0 || isProcessingQueue.value,
  )

  // 辅助函数
  function findNextMeaningfulEvent(
    events: BaseSSEData[],
    startIndex: number = 1,
  ): BaseSSEData | null {
    return (
      events
        .slice(startIndex)
        .find(
          (event) =>
            event.event_type === 'message' ||
            (event.event_type === 'play_video' && !event.data.is_background),
        ) || null
    )
  }

  // 查找所有特定类型的事件
  function findAllEvents(
    events: BaseSSEData[],
    eventType: string,
  ): BaseSSEData[] {
    return events.filter((event) => event.event_type === eventType)
  }

  function updateNextEventFlags(nextEvent: BaseSSEData | null) {
    const messagesStore = useChatMessagesStore()
    messagesStore.hasNextMessage = nextEvent?.event_type === 'message'
    messagesStore.hasNextVideo =
      nextEvent?.event_type === 'play_video' && !nextEvent.data.is_background
  }

  // Actions
  /**
   * 处理事件
   */
  async function handleEvent(parsedData: BaseSSEData) {
    const uiStore = useChatUIStore()
    if (parsedData.event_type === 'preload') {
      return
    }
    // Disable input when new events arrive
    if (parsedData.event_type !== 'preload') {
      uiStore.setDisableInput(true)
    }

    // 如果是资源相关事件，立即开始预加载，与队列处理并行
    const resourcesStore = useChatResourcesStore()
    if (
      parsedData.event_type === 'play_video' ||
      parsedData.event_type === 'play_video_group' ||
      parsedData.event_type === 'show_image' ||
      parsedData.event_type === 'animated_images' ||
      parsedData.event_type === 'preload'
    ) {
      // 立即预加载资源，但不阻塞队列处理
      resourcesStore.preloadEventResources(parsedData).catch((err) => {
        console.warn('Resource preloading failed:', err)
      })
    }

    // 所有事件都加入队列进行处理
    messageQueue.value.push(parsedData)
    messageQueue.value.sort((a, b) => a.timestamp - b.timestamp)
  }

  /**
   * 处理场景转换事件
   */
  async function handleSceneTransitionEvent(data: any) {
    const targetSceneId = data.data?.target_scene_id

    // 如果有目标场景ID，存储起来（meetup子场景的continue按钮会检查并使用）
    if (targetSceneId) {
      pendingTargetScene.value = targetSceneId
      console.log('Storing pending target scene:', targetSceneId)
    }

    if (!isShouldRestart.value) return

    // 获取转换参数
    const seconds = data.data?.seconds || 0

    // 设置定时器
    let remainingSeconds = seconds
    if (seconds > 0) {
      countdownInterval.value = setInterval(() => {
        // 样式化输出倒计时
        const countdownStyle =
          'background: #ff5722; color: white; padding: 2px 5px; border-radius: 3px; font-weight: bold;'

        remainingSeconds--

        // 当倒计时结束时，清除定时器
        if (remainingSeconds < 0) {
          clearInterval(countdownInterval.value)
        }
      }, 1000)
      // 设置场景转换的触发器
      timeoutId.value = setTimeout(() => {
        // 发送一个空消息，触发场景切换
        sendMessage(
          '__NEXT_SCENE__',
          '__NEXT_SCENE__',
          'text',
          undefined,
          null,
          undefined,
          true,
        )
      }, seconds * 1000)
    } else {
      sendMessage('', null, 'config', undefined, null, 0, true)
    }
  }

  /**
   * 处理场景显示事件
   */
  async function handleShowSceneEvent(data: any) {
    // 如果这个事件的场景跟lastScene不同，则是场景发生了变化，如果有计时器场景，则需要清除
    if (
      lastScene.value &&
      lastScene.value !== data.data.scene_id &&
      countdownInterval.value &&
      timeoutId.value
    ) {
      clearInterval(countdownInterval.value)
      clearTimeout(timeoutId.value)
    }
    const scene = data.data.scene_id
    if (scene) {
      const storyStore = useStoryStore()
      const currentTimestamp = Date.now()
      const messagesStore = useChatMessagesStore()

      // 上报场景变化
      reportEvent(ReportEvent.SceneChange, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor?.id,
        currentScene: scene,
      })
      if (window.fbq) {
        window.fbq('trackCustom', 'SceneChange', {
          scene: scene,
          storyId: storyStore.currentStory?.id,
          actorId: storyStore.currentActor?.id,
        })
      }

      // 如果有上一个场景，计算并上报耗时
      if (
        lastSceneTimestamp.value &&
        lastScene.value &&
        lastScene.value !== scene
      ) {
        const duration = currentTimestamp - lastSceneTimestamp.value
        reportEvent(ReportEvent.SceneDuration, {
          storyId: storyStore.currentStory?.id,
          actorId: storyStore.currentActor?.id,
          fromScene: lastScene.value,
          toScene: scene,
          duration, // 耗时，单位毫秒
        })
      }

      // 检查新场景是否有voice_config事件
      // 如果queue中没有voice_config事件，则重置currentVoiceId
      const hasVoiceConfig = messageQueue.value.some(
        (event) => event.event_type === 'voice_config',
      )

      if (!hasVoiceConfig && lastScene.value !== scene) {
        // 如果新场景没有语音配置事件，重置为默认语音
        messagesStore.resetVoiceConfig()
      }

      // 更新场景信息
      lastScene.value = scene
      currentScene.value = scene // 存储当前场景ID，供Chat4等功能使用
      lastSceneTimestamp.value = currentTimestamp

      // 通知Chat4状态机场景变化（如果在Chat4环境中）
      if (window.location.pathname.includes('/chat4/')) {
        // 通过自定义事件通知Chat4状态机
        window.dispatchEvent(
          new CustomEvent('chat4ServerSceneChange', {
            detail: { sceneId: scene },
          }),
        )
      }

      // 重置付费场景状态
      clearPaidSceneInterval()
    }
  }

  /**
   * 处理支付需求事件
   */
  async function handlePaymentRequiredEvent() {
    // 显示钻石用完提示模态框，而不是直接显示充值弹窗
    paymentRequired.value = true
    // 清空消息队列，因为需要先处理支付
    messageQueue.value = []
    isProcessingQueue.value = false

    // 锁定聊天输入框
    const uiStore = useChatUIStore()
    uiStore.setDisableInput(true)
  }

  /**
   * 处理进入付费场景事件
   */
  async function handleEnterPaidSceneEvent(data: any) {
    // 清除之前的计时器（如果存在）
    clearPaidSceneInterval()
    // 设置为付费场景状态
    inPaidScene.value = true

    // 从事件数据中获取付费模式和持续时间
    const { mode, duration } = data.data || { mode: 'timer', duration: 300 }

    billingInterval.value = duration

    // 获取当前故事ID和角色ID
    const storyStore = useStoryStore()
    const storyId = storyStore.currentStory?.id
    const actorId = currentActorId.value

    // 如果没有故事ID，则无法继续
    if (!storyId) {
      console.error('无法获取当前故事ID，无法启动付费场景计时器')
      return
    }

    // 如果不是计时模式或持续时间无效，则不启动计时器
    if (mode !== 'timer' || duration <= 0) {
      return
    }

    // 准备API请求参数
    const params = {
      story_id: storyId,
      // 只有当actorId存在且不等于"reasoning"时才传递actor_id参数
      ...(actorId && actorId !== 'reasoning' ? { actor_id: actorId } : {}),
    }

    // 显示付费场景提醒对话框
    showPaidSceneDialog.value = true

    // 启动计时器
    await startPaidSceneTimer(params)
  }

  /**
   * 启动付费场景计时器
   */
  async function startPaidSceneTimer(params: any) {
    try {
      // 获取当前关卡耗时
      const response = await getCurrentPlaytime(params)
      // 检查响应是否有效
      if (!response || !response.data) {
        console.error('获取关卡耗时失败: 无效的响应')
        return
      }
      const currentPlaytime = response.data.duration

      // 设置计时器，每3秒检查一次是否需要重置时间
      paidSceneInterval.value = setInterval(
        () => checkAndResetPlaytime(params),
        10000,
      )
    } catch (error) {
      console.error('获取当前关卡耗时失败:', error)
    }
  }

  /**
   * 检查并重置关卡耗时
   */
  async function checkAndResetPlaytime(params: any) {
    try {
      // 获取最新的关卡耗时
      const userStore = useUserStore()
      const latestResponse = await getCurrentPlaytime(params)

      // 检查响应是否有效
      if (!latestResponse || !latestResponse.data) {
        console.error('获取关卡耗时失败: 无效的响应')
        return
      }
      const latestPlaytime = latestResponse.data.duration

      console.log(
        '当前已消耗时间:',
        latestPlaytime,
        '秒，计费间隔:',
        billingInterval.value,
        '秒',
      )

      // 如果耗时未达到计费间隔，则不需要重置
      if (!billingInterval.value || latestPlaytime < billingInterval.value) {
        return
      }

      // 调用重置时间的接口
      const resetResponse = await resetPlaytime(params)

      // 如果重置成功
      if (resetResponse.code === '0') {
        try {
          await userStore.getUserInfo()
        } catch (error) {
          console.error('刷新用户信息失败:', error)
        }
        return
      }

      // 重置失败处理
      console.error('重置时间失败:', '未知原因')

      // 金币不足，显示钻石用完提示模态框
      // 设置状态，让DiamondUsedUpModal显示
      paymentRequired.value = true

      // 清除计时器
      clearPaidSceneInterval()

      // 锁定聊天输入框
      const uiStore = useChatUIStore()
      uiStore.setDisableInput(true)

      // 确保充值弹窗不会自动显示
      const rechargeStore = useRechargeStore()
      rechargeStore.visible = false
    } catch (error) {
      console.error('获取或重置关卡耗时失败:', error)
      // 发生错误时清除计时器，避免持续报错
      clearPaidSceneInterval()
    }
  }

  /**
   * 处理错误事件
   */
  async function handleErrorEvent(data: any) {
    error.value = data.data.message || 'An error occurred'
  }

  /**
   * 处理调试事件
   */
  async function handleDebugEvent(data: any) {
    if (!data.data.debug_messages) return

    const timestamp = new Date()
      .toLocaleTimeString()
      .replace(/:\d{2}\.\d{3}\s\w+/, '')
    let count = 0

    data.data.debug_messages.forEach((message: string) => {
      setTimeout(() => {
        console.log(
          '%c[' + timestamp + ']%c %c[DEBUG]%c ' + message,
          'color: #666; font-family: monospace;',
          '',
          'background: #000; color: #0f0; font-family: monospace; padding: 2px 4px; border-radius: 2px; text-shadow: 0 0 2px #0f0;',
          'color: #2196f3; font-family: monospace; text-shadow: 0 0 1px rgba(33,150,243,0.5);',
        )
      }, count * 100)
      count++
    })
  }

  /**
   * 处理等待事件
   */
  async function handleWaitEvent(data: any) {
    await new Promise((resolve) =>
      setTimeout(resolve, data.data.seconds * 1000),
    )
  }

  /**
   * 处理更新用户金币事件
   */
  function handleUpdateUserCoinsEvent(data: any) {
    const userStore = useUserStore()
    const { coins } = data.data
    userStore.userInfo.coins = coins
  }

  /**
   * 处理上传图片事件
   */
  function handleUploadImageEvent(_data: any) {
    // 实现上传图片逻辑
  }

  /**
   * 处理朋友圈显示事件
   */
  async function handleShowMomentsEvent(data: any) {
    // 直接更新chat4 store中的朋友圈数据
    const { useChat4Store } = await import('@/store/chat4')
    const chat4Store = useChat4Store()

    if (data.data) {
      chat4Store.updateMomentPosts(data.data)
    }
  }

  /**
   * 处理日记显示事件
   */
  async function handleShowDiaryEvent(data: any) {
    // 更新chat4 store中的日记数据
    const { useChat4Store } = await import('@/store/chat4')
    const chat4Store = useChat4Store()

    if (data.data) {
      chat4Store.updateDiaryData(data.data)
    }
  }

  /**
   * 处理随机预设资源事件
   */
  async function handleRandomPresetEvent(data: any) {
    const resourcesStore = useChatResourcesStore()

    // 验证数据格式
    if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
      console.warn('Invalid random_preset event data:', data)
      return
    }

    // 随机选择一个资源项
    const randomIndex = Math.floor(Math.random() * data.data.length)
    const selectedItem = data.data[randomIndex]

    // 验证选中项的格式
    if (!selectedItem || !selectedItem.type || !selectedItem.url) {
      console.warn('Invalid random_preset item:', selectedItem)
      return
    }

    console.log('Random preset selected:', selectedItem)

    // 根据类型调用相应的处理函数
    if (selectedItem.type === 'video') {
      // 构造视频事件数据格式
      const videoEventData = {
        data: {
          url: selectedItem.url,
          tag: selectedItem.tag,
          is_background: true, // 默认是背景视频，可根据需要调整
        },
      }
      await resourcesStore.handlePlayVideoEvent(videoEventData)
    } else if (selectedItem.type === 'img') {
      // 构造图片事件数据格式
      const imageEventData = {
        data: {
          url: selectedItem.url,
          tag: selectedItem.tag,
          is_fullscreen: false, // 默认不是全屏图片，可根据需要调整
        },
      }
      await resourcesStore.handleShowImageEvent(imageEventData)
    } else {
      console.warn('Unsupported random_preset type:', selectedItem.type)
    }
  }

  /**
   * 处理弹幕显示事件 - 转发给 Chat4 处理
   * Chat4 专用功能，通过自定义事件转发给 useChat4Events 处理
   */
  function handleShowDanmakuEvent(data: any) {
    console.log('Forwarding show_danmaku event to Chat4:', data)

    // 直接转发原始数据给 Chat4 处理
    window.dispatchEvent(
      new CustomEvent('chat4DanmakuEvent', {
        detail: { eventData: data },
      }),
    )
  }

  // 场景行为菜单数据
  const sceneActionsMenu = ref<
    Array<{
      key: string
      label: string
      icon: string
      level?: number
      imageUrl?: string // 新增：图片URL
      requirement?: string // 新增：解锁要求类型
      value?: number // 新增：解锁要求值
    }>
  >([])

  // 好感度系统相关状态
  const favorabilityState = ref<FavorabilityState>({
    currentHeartValue: 0,
    currentLevel: 'level0',
    nextLevel: undefined,
    nextLevelHeartValue: undefined,
    heartValueLeft: undefined,
    levelInfos: [],
    sceneConditions: [],
  })

  /**
   * 处理显示场景解锁条件事件
   */
  function handleShowScenesUnlockConditionsEvent(
    data: ShowScenesUnlockConditionsEventData,
  ) {
    console.log('Handling show scenes unlock conditions event:', data)

    try {
      const { scene_conditions = [] } = data.data || {}

      if (!Array.isArray(scene_conditions)) {
        console.warn('Invalid scene conditions data format:', data)
        return
      }

      favorabilityState.value.sceneConditions = scene_conditions
      console.log('Scene unlock conditions updated:', scene_conditions)

      // 发送自定义事件通知组件更新
      window.dispatchEvent(
        new CustomEvent('scenesUnlockConditionsUpdated', {
          detail: { sceneConditions: scene_conditions },
        }),
      )
    } catch (error) {
      console.error(
        'Error handling show_scenes_unlock_conditions event:',
        error,
      )
    }
  }

  /**
   * 处理显示等级信息事件
   */
  function handleShowLevelInfoEvent(data: ShowLevelInfoEventData) {
    console.log('Handling show level info event:', data)

    try {
      const { level_infos = [] } = data.data || {}

      if (!Array.isArray(level_infos)) {
        console.warn('Invalid level infos data format:', data)
        return
      }

      favorabilityState.value.levelInfos = level_infos

      // 更新当前等级和下一等级信息
      updateFavorabilityLevelInfo()

      console.log('Level infos updated:', level_infos)

      // 发送自定义事件通知组件更新
      window.dispatchEvent(
        new CustomEvent('levelInfoUpdated', {
          detail: {
            levelInfos: level_infos,
            favorabilityState: favorabilityState.value,
          },
        }),
      )
    } catch (error) {
      console.error('Error handling show_level_info event:', error)
    }
  }

  /**
   * 更新好感度等级信息
   */
  function updateFavorabilityLevelInfo() {
    const { currentHeartValue, levelInfos } = favorabilityState.value

    // 使用统一的等级计算逻辑
    const currentLevel = SceneUnlockUtils.calculateCurrentLevel(
      currentHeartValue,
      levelInfos,
    )

    // 计算下一等级信息
    let nextLevel: string | undefined
    let nextLevelHeartValue: number | undefined

    // 按好感度值排序等级信息
    const sortedLevels = [...levelInfos].sort(
      (a, b) => a.heart_value - b.heart_value,
    )

    // 找到下一等级
    for (const level of sortedLevels) {
      if (currentHeartValue < level.heart_value) {
        nextLevel = level.level
        nextLevelHeartValue = level.heart_value
        break
      }
    }

    // 计算距离下一等级还需要的好感度值
    const heartValueLeft = nextLevelHeartValue
      ? nextLevelHeartValue - currentHeartValue
      : undefined

    favorabilityState.value.currentLevel = currentLevel
    favorabilityState.value.nextLevel = nextLevel
    favorabilityState.value.nextLevelHeartValue = nextLevelHeartValue
    favorabilityState.value.heartValueLeft = heartValueLeft

    console.log('Favorability level info updated:', {
      currentLevel,
      nextLevel,
      nextLevelHeartValue,
      heartValueLeft,
    })

    // 更新场景解锁状态
    updateSceneUnlockStatuses()
  }

  /**
   * 更新所有场景的解锁状态
   */
  function updateSceneUnlockStatuses() {
    const { sceneConditions, levelInfos, currentHeartValue } =
      favorabilityState.value
    const userStore = useUserStore()
    const currentCoins = userStore.userInfo?.coins || 0

    // 发送自定义事件通知组件更新场景解锁状态
    window.dispatchEvent(
      new CustomEvent('sceneUnlockStatusUpdated', {
        detail: {
          sceneConditions,
          levelInfos,
          currentHeartValue,
          currentCoins,
        },
      }),
    )
  }

  /**
   * 计算指定场景的解锁状态
   */
  function getSceneUnlockStatus(sceneId: string): SceneUnlockStatus {
    const { sceneConditions, levelInfos, currentHeartValue } =
      favorabilityState.value
    const userStore = useUserStore()
    const currentCoins = userStore.userInfo?.coins || 0

    return SceneUnlockUtils.calculateSceneUnlockStatus(
      sceneId,
      sceneConditions,
      levelInfos,
      currentHeartValue,
      currentCoins,
    )
  }

  /**
   * 处理场景行为菜单事件
   */
  function handleSceneActionsMenuEvent(data: any) {
    console.log('Handling scene actions menu event:', data)

    try {
      const { actions = [] } = data.data || {}

      if (!Array.isArray(actions)) {
        console.warn('Invalid scene actions menu data format:', data)
        return
      }

      sceneActionsMenu.value = actions.map((action: any) => ({
        key: action.key || action.id || '',
        label: action.label || action.name || '',
        icon: action.icon || '',
        level: action.level || null,
      }))

      console.log('Scene actions menu updated:', sceneActionsMenu.value)
    } catch (error) {
      console.error('Error handling scene_actions_menu event:', error)
    }
  }

  /**
   * 处理显示场景动作菜单事件 (show_scene_action_menu)
   */
  async function handleShowSceneActionMenuEvent(data: any) {
    console.log('Handling show_scene_action_menu event:', data)

    try {
      const { menus = [] } = data.data || {}

      if (!Array.isArray(menus)) {
        console.warn('Invalid show_scene_action_menu data format:', data)
        return
      }

      // 使用 Chat4 Store 来管理场景行为菜单
      const { useChat4Store } = await import('./chat4')
      const chat4Store = useChat4Store()
      chat4Store.updateSceneActionsMenu(menus)

      // 同时更新本地的 sceneActionsMenu 以保持兼容性
      sceneActionsMenu.value = menus.map((menu: any) => ({
        key: menu.key || '',
        label: menu.name || '',
        icon: menu.key || '',
        imageUrl: menu.image_url || '',
        requirement: menu.requirement || null,
        value: menu.value || 0,
        level: menu.requirement === 'heart_value' ? menu.value : null,
      }))

      console.log('Scene action menu updated from show_scene_action_menu')
    } catch (error) {
      console.error('Error handling show_scene_action_menu event:', error)
    }
  }

  /**
   * 处理场景变化事件 - 通用场景行为处理器
   * 根据当前场景类型执行不同的行为
   */
  function handleSceneChangeEvent(data: any) {
    // 验证事件数据格式
    if (!SceneUtils.validateSceneChangeEvent(data)) {
      console.warn('Invalid scene change event data:', data)
      return
    }

    const sceneId = data.data.scene_id
    const eventTimestamp = data.timestamp
    const delaySeconds = data.data.seconds // 延迟秒数

    // 记录场景ID，用于后续的场景跳转
    if (sceneId) {
      recordedSceneId.value = sceneId
      console.log('Scene ID recorded for future jumps:', sceneId)
    }

    // 使用工具类获取场景类型
    const currentSceneType = SceneUtils.getSceneType(currentScene.value)

    console.log('Scene change event triggered:', {
      sceneId,
      currentScene: currentScene.value,
      sceneType: currentSceneType,
      timestamp: eventTimestamp,
      delaySeconds,
    })

    // 执行场景变化处理的函数
    const executeSceneAction = () => {
      // 如果有延迟，需要重新检查当前场景是否匹配
      if (delaySeconds && delaySeconds > 0) {
        const currentSceneTypeAtExecution = SceneUtils.getSceneType(
          currentScene.value,
        )
        if (currentSceneTypeAtExecution !== currentSceneType) {
          console.log('Scene changed during delay, skipping action:', {
            originalScene: currentSceneType,
            currentScene: currentSceneTypeAtExecution,
            delaySeconds,
          })
          return
        }
        console.log('Executing delayed scene action:', {
          sceneType: currentSceneType,
          delaySeconds,
          currentScene: currentScene.value,
        })
      }

      // 根据场景类型执行不同的行为
      switch (currentSceneType) {
        case SceneType.LIVING:
          // Living场景：显示好友请求弹窗
          handleFriendRequestAction(sceneId, eventTimestamp)
          break

        case SceneType.PHONE:
          // 聊天场景：可以添加其他行为
          handleChatSceneAction(sceneId, eventTimestamp)
          break

        case SceneType.DEFAULT:
        default:
          // 默认场景：记录日志
          handleDefaultSceneAction(sceneId, eventTimestamp)
          break
      }
    }

    // 如果有延迟参数，延迟执行；否则立即执行
    if (delaySeconds && delaySeconds > 0) {
      console.log(`Delaying scene action by ${delaySeconds} seconds`)
      setTimeout(executeSceneAction, delaySeconds * 1000)
    } else {
      executeSceneAction()
    }
  }

  /**
   * 处理好友请求行为 - Living场景专用
   */
  function handleFriendRequestAction(sceneId: string, timestamp: number) {
    // 创建好友请求事件详情
    const eventDetail: SceneCustomEventDetail = {
      sceneId,
      timestamp,
      action: SceneActionType.SHOW_FRIEND_REQUEST,
      params: {
        sceneType: SceneType.LIVING,
      },
    }

    // 使用工具类创建自定义事件
    const event = SceneUtils.createSceneEvent('showFriendRequest', eventDetail)
    window.dispatchEvent(event)

    console.log('Friend request popup triggered for Living scene:', eventDetail)
  }

  /**
   * 处理聊天场景行为
   */
  function handleChatSceneAction(sceneId: string, timestamp: number) {
    // 创建聊天场景事件详情
    const eventDetail: SceneCustomEventDetail = {
      sceneId,
      timestamp,
      action: SceneActionType.CHAT_SCENE_CHANGE,
      params: {
        sceneType: SceneType.PHONE,
      },
    }

    // 使用工具类创建自定义事件
    const event = SceneUtils.createSceneEvent('chatSceneAction', eventDetail)
    window.dispatchEvent(event)

    console.log('Chat scene action triggered:', eventDetail)

    // 可以在这里添加聊天场景特有的行为
    // 例如：显示聊天提示、更新聊天状态等
  }

  /**
   * 处理默认场景行为
   */
  function handleDefaultSceneAction(sceneId: string, timestamp: number) {
    // 创建默认场景事件详情
    const eventDetail: SceneCustomEventDetail = {
      sceneId,
      timestamp,
      action: SceneActionType.DEFAULT_SCENE_CHANGE,
      params: {
        sceneType: SceneType.DEFAULT,
      },
    }

    // 使用工具类创建自定义事件
    const event = SceneUtils.createSceneEvent('defaultSceneAction', eventDetail)
    window.dispatchEvent(event)

    console.log('Default scene action triggered:', eventDetail)

    // 可以在这里添加默认的场景变化行为
  }

  /**
   * 触发下一个事件
   */
  async function triggerNextEvent() {
    if (messageQueue.value.length > 0) {
      const currentIndex = messageQueue.value.findIndex(
        (event) =>
          event.event_type === 'message' ||
          (event.event_type === 'play_video' && !event.data.is_background),
      )
      if (currentIndex !== -1) {
        // Remove all events up to and including the current one
        messageQueue.value.splice(0, currentIndex + 1)
        // Process the next event
        await processEventQueue()
      }
    }
  }

  /**
   * 处理下一个单个事件（用于手动控制）
   */
  async function processNextEvent() {
    if (isProcessingQueue.value || messageQueue.value.length === 0) return

    const messagesStore = useChatMessagesStore()
    const resourcesStore = useChatResourcesStore()
    const uiStore = useChatUIStore()
    const tasksStore = useChatTasksStore()

    // 找到下一个消息或非背景视频事件
    const currentIndex = messageQueue.value.findIndex(
      (event) =>
        event.event_type === 'message' ||
        (event.event_type === 'play_video' && !event.data.is_background),
    )

    if (currentIndex === -1) return

    // 创建事件处理器映射
    const eventHandlerMap: Record<string, (data: any) => Promise<void> | void> =
      {
        message: messagesStore.handleMessageEvent,
        play_video: resourcesStore.handlePlayVideoEvent,
        play_video_group: resourcesStore.handlePlayVideoGroupEvent,
        wait: handleWaitEvent,
        show_chat_options: resourcesStore.handleShowChatOptionsEvent,
        show_action_options: resourcesStore.handleShowActionOptionsEvent,
        error: handleErrorEvent,
        show_image: resourcesStore.handleShowImageEvent,
        show_ending: uiStore.handleShowEndingEvent,
        show_overlay: uiStore.handleShowOverlayEvent,
        payment_required: handlePaymentRequiredEvent,
        preload: resourcesStore.handlePreloadEvent,
        play_audio: handlePlayAudioEvent,
        update_task_progress: tasksStore.handleUpdateTaskProgressEvent,
        update_user_coins: handleUpdateUserCoinsEvent,
        animated_images: (data) =>
          resourcesStore.handleAnimatedImagesEvent(data, isShouldRestart.value),
        update_total_progress: tasksStore.handleUpdateTotalProgressEvent,
        upload_image: handleUploadImageEvent,
        debug: handleDebugEvent,
        heart_value: (data) => {
          messagesStore.handleHeartValueEvent(data)
          // 同时更新好感度状态
          favorabilityState.value.currentHeartValue = data.data.heart_value
          updateFavorabilityLevelInfo()
        },
        show_alert: uiStore.handleShowAlertEvent,
        show_scene: handleShowSceneEvent,
        scene_transition: handleSceneTransitionEvent,
        voice_config: messagesStore.handleVoiceConfigEvent,
        enter_paid_scene: handleEnterPaidSceneEvent,
        mission_completed: (data) => handleMissionCompletedEvent(data, false),
        handle_scene_change: handleSceneChangeEvent,
        show_friend_request: handleSceneChangeEvent,
        show_danmaku: handleShowDanmakuEvent,
        scene_actions_menu: handleSceneActionsMenuEvent,
        show_scene_action_menu: handleShowSceneActionMenuEvent,
        show_scenes_unlock_conditions: handleShowScenesUnlockConditionsEvent,
        show_level_info: handleShowLevelInfoEvent,
        random_preset: handleRandomPresetEvent,
        show_chat4_card: resourcesStore.handleShowChatOptionsEvent,
      }

    try {
      isProcessingQueue.value = true
      uiStore.setDisableInput(true)

      // 处理当前事件之前的所有背景事件
      for (let i = 0; i < currentIndex; i++) {
        const backgroundEvent = messageQueue.value[0]
        messageQueue.value.shift()

        const handler = eventHandlerMap[backgroundEvent.event_type]
        if (handler) {
          try {
            await handler(backgroundEvent)
          } catch (error) {
            console.error(
              `Error handling background event ${backgroundEvent.event_type}:`,
              error,
            )
          }
        }
      }

      // 处理目标事件
      const currentEvent = messageQueue.value[0]
      messageQueue.value.shift()

      const handler = eventHandlerMap[currentEvent.event_type]
      if (handler) {
        try {
          if (currentEvent.event_type === 'message') {
            // 对于消息事件，需要特殊处理以支持打字效果
            const nextEvent = findNextMeaningfulEvent(messageQueue.value)
            updateNextEventFlags(nextEvent)

            await handler(currentEvent)

            // 等待打字效果完成
            if (messagesStore.messageTypingPromise) {
              await messagesStore.messageTypingPromise
            }
          } else {
            await handler(currentEvent)
          }
        } catch (error) {
          console.error(
            `Error handling event ${currentEvent.event_type}:`,
            error,
          )
        }
      } else {
        console.warn(`Unhandled event type: ${currentEvent.event_type}`)
      }
    } finally {
      isProcessingQueue.value = false

      // 只有在没有更多事件且没有选项时才启用输入
      if (
        messageQueue.value.length === 0 &&
        !resourcesStore.chatOptions.value?.length &&
        !resourcesStore.actionOptions.value?.length &&
        !resourcesStore.chat4Card.value?.length
      ) {
        uiStore.setDisableInput(false)
      }
    }
  }

  /**
   * 检查是否有下一个可处理的事件
   */
  function hasNextProcessableEvent() {
    return (
      messageQueue.value.findIndex(
        (event) =>
          event.event_type === 'message' ||
          (event.event_type === 'play_video' && !event.data.is_background),
      ) !== -1
    )
  }

  /**
   * 处理背景相关事件（在视频播放的同时处理）
   */
  async function processBackgroundEvents() {
    if (messageQueue.value.length === 0) return

    const resourcesStore = useChatResourcesStore()

    // 查找队列中的第一个背景相关事件
    const nextBackgroundEvent = messageQueue.value.find(
      (event) =>
        event.event_type === 'show_image' ||
        (event.event_type === 'play_video' && event.data.is_background) ||
        event.event_type === 'animated_images',
    )

    if (!nextBackgroundEvent) return

    // 只处理队列中的第一个背景相关事件，保持背景变化的顺序
    try {
      if (nextBackgroundEvent.event_type === 'show_image') {
        // 直接设置背景图片，但不触发其他效果
        if (resourcesStore.currentActorId && nextBackgroundEvent.data.url) {
          resourcesStore.backgroundImageMap[resourcesStore.currentActorId] =
            nextBackgroundEvent.data.url
          resourcesStore.backgroundImageCouldBeFullScreen =
            nextBackgroundEvent.data.is_fullscreen || false
          console.log('预处理背景图片:', nextBackgroundEvent.data.url)
        }
      } else if (
        nextBackgroundEvent.event_type === 'play_video' &&
        nextBackgroundEvent.data.is_background
      ) {
        // 设置背景视频
        if (resourcesStore.currentActorId && nextBackgroundEvent.data.url) {
          resourcesStore.backgroundVideoMap[resourcesStore.currentActorId] = {
            url: nextBackgroundEvent.data.url,
            notLoop: !!nextBackgroundEvent.data.not_loop,
          }
          console.log('预处理背景视频:', nextBackgroundEvent.data.url)
        }
      } else if (nextBackgroundEvent.event_type === 'animated_images') {
        // 设置动画图片序列
        const { urls } = nextBackgroundEvent.data
        if (resourcesStore.currentActorId && urls && urls.length > 0) {
          resourcesStore.animatedImagesMap[resourcesStore.currentActorId] = urls
          console.log('预处理动画图片序列:', urls.length, '张图片')
        }
      }
    } catch (error) {
      console.error(
        `Error pre-processing background event ${nextBackgroundEvent.event_type}:`,
        error,
      )
    }
  }

  /**
   * 处理事件队列
   */
  async function processEventQueue() {
    if (isProcessingQueue.value || messageQueue.value.length === 0) return

    const messagesStore = useChatMessagesStore()
    const resourcesStore = useChatResourcesStore()
    const uiStore = useChatUIStore()
    const tasksStore = useChatTasksStore()

    try {
      isProcessingQueue.value = true
      uiStore.setDisableInput(true)

      // 新增：在处理队列前，预扫描队列中的资源并预加载
      await resourcesStore.preloadQueuedResources(messageQueue.value)

      while (messageQueue.value.length > 0) {
        const currentEvent = messageQueue.value[0]
        // 只在处理消息事件时才需要查找下一个有意义的事件
        if (currentEvent.event_type === 'message') {
          const nextEvent = findNextMeaningfulEvent(messageQueue.value)
          updateNextEventFlags(nextEvent)
        }

        const eventHandlerMap: Record<
          string,
          (data: any) => Promise<void> | void
        > = {
          message: messagesStore.handleMessageEvent,
          play_video: resourcesStore.handlePlayVideoEvent,
          play_video_group: resourcesStore.handlePlayVideoGroupEvent,
          wait: handleWaitEvent,
          show_chat_options: resourcesStore.handleShowChatOptionsEvent,
          show_action_options: resourcesStore.handleShowActionOptionsEvent,
          error: handleErrorEvent,
          show_image: resourcesStore.handleShowImageEvent,
          show_ending: uiStore.handleShowEndingEvent,
          show_overlay: uiStore.handleShowOverlayEvent,
          payment_required: handlePaymentRequiredEvent,
          preload: resourcesStore.handlePreloadEvent,
          play_audio: handlePlayAudioEvent,
          update_task_progress: tasksStore.handleUpdateTaskProgressEvent,
          update_user_coins: handleUpdateUserCoinsEvent,
          animated_images: (data) =>
            resourcesStore.handleAnimatedImagesEvent(
              data,
              isShouldRestart.value,
            ),
          update_total_progress: tasksStore.handleUpdateTotalProgressEvent,
          upload_image: handleUploadImageEvent,
          debug: handleDebugEvent,
          heart_value: (data) => {
            messagesStore.handleHeartValueEvent(data)
            // 同时更新好感度状态
            favorabilityState.value.currentHeartValue = data.data.heart_value
            updateFavorabilityLevelInfo()
          },
          show_alert: uiStore.handleShowAlertEvent,
          show_scene: handleShowSceneEvent,
          scene_transition: handleSceneTransitionEvent,
          voice_config: messagesStore.handleVoiceConfigEvent,
          // 处理进入付费场景事件
          enter_paid_scene: handleEnterPaidSceneEvent,
          // 处理任务完成事件
          mission_completed: (data) => handleMissionCompletedEvent(data, false),
          // 处理场景变化事件（原show_friend_request事件重构为通用场景处理）
          handle_scene_change: handleSceneChangeEvent,
          // 兼容旧的事件名称
          show_friend_request: handleSceneChangeEvent,
          // 处理弹幕显示事件
          show_danmaku: handleShowDanmakuEvent,
          // 处理场景行为菜单事件
          scene_actions_menu: handleSceneActionsMenuEvent,
          // 处理显示场景动作菜单事件
          show_scene_action_menu: handleShowSceneActionMenuEvent,
          // 处理好感度系统事件
          show_scenes_unlock_conditions: handleShowScenesUnlockConditionsEvent,
          show_level_info: handleShowLevelInfoEvent,
          // 处理随机预设资源事件
          random_preset: handleRandomPresetEvent,
          // 处理朋友圈事件
          show_moments: handleShowMomentsEvent,
          // 处理日记事件
          show_diary: handleShowDiaryEvent,
        }

        const handler = eventHandlerMap[currentEvent.event_type]
        if (handler) {
          try {
            if (currentEvent.event_type === 'show_overlay') {
              // 移除当前事件
              messageQueue.value.shift()
              isProcessingQueue.value = false
              // 等待 overlay 处理完成
              await handler.call(this, currentEvent)
              // 重新开始处理队列
              if (messageQueue.value.length > 0) {
                await processEventQueue()
              }
              return
            } else if (currentEvent.event_type === 'message') {
              // 消息事件特殊处理
              console.log('chat4-events: Processing message event')

              messageQueue.value.shift()

              try {
                // 处理消息事件
                await handler.call(this, currentEvent)

                // 等待打字效果完成
                if (messagesStore.messageTypingPromise) {
                  await messagesStore.messageTypingPromise
                }
              } catch (error) {
                console.error(
                  'chat4-events: Error in message event processing:',
                  {
                    eventId: currentEvent.event_type,
                    error: error.message,
                  },
                )
                throw error // 重新抛出错误
              }

              // 设置下一个事件的延迟时间
              if (messagesStore.messageTypingPromise === null) {
                // 如果打字效果已完成，根据下一个事件类型设置延迟时间
                if (messagesStore.hasNextMessage) {
                  messagesStore.messageDelayTime = 3000 // 如果下一个是消息，延迟更长
                } else if (messagesStore.hasNextVideo) {
                  messagesStore.messageDelayTime = 3000 // 如果下一个是视频，延迟适中
                } else {
                  messagesStore.messageDelayTime = 300 // 默认延迟
                }

                // 消息处理完后等待设定的延迟时间
                await new Promise((resolve) =>
                  setTimeout(resolve, messagesStore.messageDelayTime),
                )
              }

              // 在延迟之后，下一个消息处理之前重置状态
              messagesStore.hasNextMessage = false
              messagesStore.hasNextVideo = false
            } else {
              // 其他事件处理前先移除队列
              messageQueue.value.shift()
              await handler.call(this, currentEvent)
            }
          } catch (error) {
            console.error(
              `Error handling event ${currentEvent.event_type}:`,
              error,
            )
            // 发生错误时也要移除当前事件，防止卡死
            if (messageQueue.value[0] === currentEvent) {
              messageQueue.value.shift()
            }
          }
        } else {
          console.warn(`Unhandled event type: ${currentEvent.event_type}`)
          messageQueue.value.shift()
        }
      }
    } finally {
      isProcessingQueue.value = false

      if (
        messageQueue.value.length === 0 &&
        !resourcesStore.chatOptions?.length &&
        !resourcesStore.actionOptions?.length
      ) {
        uiStore.setDisableInput(false)

        // 如果事件队列完全处理完成，且视频组处于暂停状态，尝试恢复
        if (resourcesStore.isVideoGroupPaused) {
          console.log('📋 事件队列处理完成，恢复暂停的视频组')
          await resourcesStore.resumeVideoGroup()
        }
      }

      // 如果还有未处理的事件，继续处理
      if (messageQueue.value.length > 0) {
        await processEventQueue()
      }
    }
  }

  /**
   * 处理播放音频事件
   */
  function handlePlayAudioEvent(data: any) {
    const { url, is_bgm } = data.data
    const audioManager = useAudioManager()

    // Early return for invalid data
    if (!url) {
      console.error('Invalid audio data: missing URL')
      return
    }

    // 使用 audioManager 处理音频播放
    if (is_bgm) {
      audioManager.playBgm(url)
    } else {
      audioManager.playSound(url)
    }
  }

  /**
   * 初始化聊天
   */
  async function initializeChat(actorId: string, storyId?: string) {
    const uiStore = useChatUIStore()
    const messagesStore = useChatMessagesStore()
    const resourcesStore = useChatResourcesStore()
    const tasksStore = useChatTasksStore()

    uiStore.setInitialized(false)
    uiStore.setLoading(true)
    error.value = null
    currentActorId.value = actorId
    resourcesStore.setCurrentActorId(actorId)
    uiStore.setInitializing(true)

    const storyStore = useStoryStore()
    const skillStore = useSkillStore()
    const finalStoryId = storyId || storyStore.currentStory?.id || 'stepsister1'

    try {
      reportEvent(ReportEvent.InitializeChat, {
        storyId: finalStoryId,
        actorId: actorId,
      })

      const params: {
        story_id: string
        game_config?: {
          skill_id: string
          numerical_values: {
            happiness: number
            intelligence: number
            strength: number
            wealth: number
          }
          user_avatar_url?: string
        }
      } = {
        story_id: finalStoryId,
      }
      if (isShouldRestart.value && skillStore.selectedSkills.length > 0) {
        params.game_config = {
          skill_id: skillStore.selectedSkills[0].id,
          numerical_values: skillStore.totalAttributes,
        }
      }
      if (resourcesStore.userAvatar?.url) {
        // 确保 game_config 对象已存在，如果不存在则创建
        if (!params.game_config) {
          // @ts-ignore
          params.game_config = {
            user_avatar_url: resourcesStore.userAvatar.url,
          }
        }
        params.game_config.user_avatar_url = resourcesStore.userAvatar.url
      }

      const events: any[] = []
      await startChatSSE(
        actorId,
        params,
        async (data) => {
          const parsedData = typeof data === 'string' ? JSONParse(data) : data
          if (parsedData) {
            events.push(parsedData)
          }
        },
        (_error) => {
          messagesStore.isActorThinking = false
          error.value = 'Failed to initialize chat'
          uiStore.setError(true)
        },
        async () => {
          // Stream closed callback
          uiStore.setInitializing(false)
          const processEvents = async (events: any[]) => {
            const isChat4 =
              storyStore.currentActor?.version === '4' ||
              storyStore.currentActor?.version === '5' ||
              storyStore.currentStory?.version === '4' ||
              window.location.pathname.includes('/chat4/')

            // 对于chat4，当isShouldRestart为false时，获取当前好感度
            if (isChat4 && !isShouldRestart.value) {
              const storyId = storyStore.currentStory?.id
              const actorId = storyStore.currentActor?.id

              if (storyId && actorId) {
                const heartValueResponse = await getCurrentHeartValue({
                  story_id: storyId,
                  actor_id: actorId,
                }).catch((error) => {
                  console.error('Failed to fetch current heart value:', error)
                  return null
                })

                if (
                  heartValueResponse?.code === '0' &&
                  heartValueResponse.data
                ) {
                  // 更新好感度状态
                  favorabilityState.value.currentHeartValue =
                    heartValueResponse.data.heart_value
                  updateFavorabilityLevelInfo()

                  // 过滤掉events中的heart_value事件，避免重复处理
                  events = events.filter(
                    (event) => event.event_type !== 'heart_value',
                  )
                }
              }
            }

            if (!isShouldRestart.value && events.length > 0 && !isChat4) {
              // Handle redirect to chat scenario
              const findEvent = (
                type: string,
                optionsParams?: any,
              ): BaseSSEData | undefined =>
                [...events].reverse().find((event) => {
                  if (type === 'play_video') {
                    return (
                      event.event_type === type &&
                      event.data.is_background === optionsParams.is_background
                    )
                  }
                  return event.event_type === type
                })

              // 找到背景相关的事件
              const backgroundEvent = events.find((event) => {
                if (event.event_type === 'play_video') {
                  return event.data.is_background === true
                }
                if (
                  event.event_type === 'animated_images' &&
                  !isShouldRestart.value
                ) {
                  // 如果是动画图片且不需要重启，将其转换为 show_image 事件
                  const lastImage = event.data.urls[event.data.urls.length - 1]
                  if (lastImage) {
                    event.event_type = 'show_image'
                    event.data = {
                      url: lastImage,
                      is_fullscreen: true,
                    }
                  }
                  return true
                }
                return event.event_type === 'show_image'
              })

              const bgmEvent = events.find(
                (event) =>
                  event.event_type === 'play_audio' &&
                  event.data.is_bgm === true &&
                  event.data.url,
              )
              const lastTotalProgress = findEvent('update_total_progress')
              const lastTaskProgress = findEvent('update_task_progress')
              const lastHeartValue = findEvent('heart_value')
              const lastChatOptions = findEvent('show_chat_options')
              const isEnding = findEvent('show_ending')
              const lastVoiceConfig = findEvent('voice_config')
              const lastEnterPaidScene = findEvent('enter_paid_scene')

              // 查找所有的 mission_completed 事件
              const allMissionCompletedEvents = findAllEvents(
                events,
                'mission_completed',
              )

              // Process events in sequence
              const eventHandlers = []

              // 只处理找到的第一个背景事件
              if (backgroundEvent) {
                eventHandlers.push(handleEvent(backgroundEvent))
              }

              if (isEnding) {
                eventHandlers.push(handleEvent(isEnding))
              } else {
                if (lastVoiceConfig)
                  eventHandlers.push(handleEvent(lastVoiceConfig))
                if (bgmEvent) eventHandlers.push(handleEvent(bgmEvent))
                if (lastTotalProgress)
                  eventHandlers.push(handleEvent(lastTotalProgress))
                if (lastTaskProgress)
                  eventHandlers.push(handleEvent(lastTaskProgress))
                if (lastHeartValue)
                  eventHandlers.push(handleEvent(lastHeartValue))
                if (lastEnterPaidScene)
                  eventHandlers.push(handleEvent(lastEnterPaidScene))

                // 处理所有的 mission_completed 事件
                if (allMissionCompletedEvents.length > 0) {
                  allMissionCompletedEvents.forEach((event) => {
                    // 修改事件处理方式，直接调用handleMissionCompletedEvent并传入skipAddToCompletedActors=true
                    eventHandlers.push(handleMissionCompletedEvent(event, true))
                  })
                }

                if (lastChatOptions)
                  eventHandlers.push(handleEvent(lastChatOptions))
              }
              await Promise.all(eventHandlers)
            } else {
              // 对于chat4且isShouldRestart为true，或者其他情况，正常处理所有事件
              // todo: 任务进度限制是前端计算出来的，且需要持久化处理，不然重定向进来任务level会变，后面需要后端处理
              tasksStore.clearTasks()
              // Process all events sequentially
              await Promise.all(events.map((event) => handleEvent(event)))
            }

            await processEventQueue()
          }

          await processEvents(events)
          uiStore.isRedirectToChat = false
        },
      )
    } catch (err) {
      error.value = 'Failed to initialize chat'
      console.error('Error initializing chat:', err)

      // 使用增强的错误处理器
      const { ErrorHandler } = await import('@/utils/errorHandler')
      ErrorHandler.handleChatError(
        err instanceof Error ? err : new Error('Chat initialization failed'),
        {
          storyId: finalStoryId,
          route: window.location.pathname,
          context: 'chat_initialization',
        },
      )
    } finally {
      uiStore.setLoading(false)
      uiStore.setInitialized(true)
    }
  }

  /**
   * 生成事件类型
   * @param optionId 选项ID
   * @param msgType 消息类型
   * @param location 当前场景位置
   * @param content 消息内容
   * @returns 事件类型字符串
   */
  function generateEventType(
    optionId: string | null | undefined,
    msgType: string,
    location?: string,
    content?: string,
    optionData?: Record<string, any>,
  ): string {
    // 如果有选项ID，则为action类型
    if (optionId) {
      return 'action'
    }

    // 根据消息类型判断
    if (msgType === 'image') {
      return 'chat:image'
    }

    // 朋友圈场景特殊处理
    if (location === 'Moment') {
      // 如果optionData中有moment_id，说明是点赞或评论操作
      if (optionData?.moment_id) {
        return 'moment_comment'
      }
      // 否则是获取朋友圈数据
      return 'get_moment'
    }

    // 其他配置类型消息
    if (msgType === 'config') {
      return 'config'
    }

    // 默认聊天类型
    return 'chat'
  }

  /**
   * 发送消息
   */
  async function sendMessage(
    content: string,
    optionId?: string | null,
    msgType: 'text' | 'image' | 'config' = 'text',
    isTelepathyComplete: boolean = false,
    sceneId?: string | null,
    delay: number = 800, // 添加延迟参数，默认800ms
    isJump?: boolean,
    extraData?: Record<string, any>, // 添加额外数据参数，用于传递 present_id 等
    location?: string, // 添加location参数，用于传递当前场景ID
  ) {
    const userStore = useUserStore()
    const storyStore = useStoryStore()
    const messagesStore = useChatMessagesStore()
    const resourcesStore = useChatResourcesStore()
    const uiStore = useChatUIStore()

    // if (
    //   !currentActorId.value ||
    //   (!content.trim() && !isTelepathyComplete && msgType !== 'config') ||
    //   messagesStore.isActorThinking
    // )
    //   return

    if (resourcesStore.actionOptions?.length) {
      resourcesStore.actionOptions = []
    }

    // if (uiStore.hideInput) {
    //   uiStore.setHideInput(false)
    // }

    const id = uuidv4()
    // 添加用户消息
    const userMessage: any = {
      id,
      msg_type: msgType,
      sender_type: 'user',
      content: {
        text: content.trim(),
        media_url: msgType === 'image' ? content : null,
      },
      create_time: new Date().toISOString(),
      sender: {
        avatar_url: '',
        name: 'You',
      },
    }

    if (
      msgType !== 'image' &&
      !['__CANCEL__', '__NEXT_SCENE__'].includes(content) &&
      msgType !== 'config'
    ) {
      messagesStore.messages.push(userMessage)
    }

    uiStore.setStreaming(true)
    messagesStore.isActorThinking = true
    uiStore.setLoading(true)

    if (optionId) {
      reportEvent(ReportEvent.SendChatMessage, {
        userId: userStore.userInfo?.uuid,
        message: content,
      })
    }

    const handledOptionId =
      optionId || (resourcesStore.chatOptions?.length ? '__NULL__' : null)
    const option_data = isJump
      ? null
      : {
          actor_id:
            storyStore.currentStory?.version === '3'
              ? resourcesStore.currentActorId
              : storyStore.currentActor?.id,
          location: location || null, // 添加location字段
          // 如果是朋友圈场景，将extraData合并到option_data中
          ...(location === 'Moment' ? extraData : {}),
        }
    try {
      const events: any[] = []
      const startTime = Date.now()
      sendMessageSSE(
        storyStore.currentStory?.id,
        currentActorId.value,
        {
          event_type: generateEventType(
            optionId,
            msgType,
            location,
            content,
            extraData, // 这里的extraData实际上会成为option_data
          ),
          timestamp: Date.now(),
          data: {
            text: msgType === 'image' ? null : content,
            option_id: handledOptionId,
            image: {
              url: msgType === 'image' ? content : null,
            },
            scene_id: isTelepathyComplete ? sceneId || '' : null,
            option_data: option_data,
            // 对于非朋友圈场景，保留原有行为
            ...(location !== 'Moment' ? extraData : {}),
          },
        },
        async (data) => {
          const parsedData = typeof data === 'string' ? JSONParse(data) : data
          if (parsedData) {
            events.push(parsedData)
          }
        },
        (_error) => {
          messagesStore.isActorThinking = false
          error.value = 'Failed to send message'
          uiStore.setLoading(false)
        },
        async () => {
          // Stream closed callback
          const responseTime = Date.now() - startTime
          // 只有当服务器响应时间小于设置的延迟时间时，才需要额外等待
          if (responseTime < delay) {
            await new Promise((resolve) =>
              setTimeout(resolve, delay - responseTime),
            )
          }

          messagesStore.isActorThinking = false
          uiStore.setLoading(false)

          for (const event of events) {
            await handleEvent(event)
          }

          await processEventQueue()
        },
      )
    } catch (err) {
      messagesStore.isActorThinking = false
      uiStore.setLoading(false)
      console.error('Error sending message:', err)
      error.value = 'Failed to send message'
    } finally {
      if (resourcesStore.chatOptions?.length) {
        resourcesStore.chatOptions = []
      }

      uiStore.setInputPlaceholder('')
      uiStore.setStreaming(false)
    }
  }

  /**
   * 清空聊天
   */
  function clearChat() {
    const messagesStore = useChatMessagesStore()
    const resourcesStore = useChatResourcesStore()
    const uiStore = useChatUIStore()
    const tasksStore = useChatTasksStore()

    messagesStore.$reset()
    resourcesStore.$reset()
    uiStore.$reset()
    tasksStore.$reset()

    messageQueue.value = []
    isProcessingQueue.value = false
    error.value = null
    lastSceneTimestamp.value = null
    lastScene.value = null

    isConfirmedLeave.value = false

    // 清除线索
    gameClues.value = []

    // 清除好感
    favorabilityState.value = {
      currentHeartValue: 0,
      currentLevel: 'level0',
      nextLevel: undefined,
      nextLevelHeartValue: undefined,
      heartValueLeft: undefined,
      levelInfos: [],
      sceneConditions: [],
    }

    if (countdownInterval.value) {
      clearInterval(countdownInterval.value)
      countdownInterval.value = null
    }

    if (timeoutId.value) {
      clearTimeout(timeoutId.value)
      timeoutId.value = null
    }

    // 清除付费场景计时器
    clearPaidSceneInterval()
  }

  /**
   * 设置是否应该重启
   */
  function setShouldRestart(value: boolean) {
    isShouldRestart.value = value
  }

  // 记录已完成任务的角色
  const completedMissionActors = ref<string[]>([])

  // 存储游戏线索
  interface Clue {
    text: string
    scene_id: string
  }
  const gameClues = ref<Clue[]>([])

  /**
   * 处理任务完成事件
   * @param data 事件数据
   * @param skipAddToCompletedActors 是否跳过添加到已完成任务的角色列表中
   */
  function handleMissionCompletedEvent(
    data: any,
    skipAddToCompletedActors: boolean = false,
  ) {
    // 如果有角色名称，则添加到已完成任务的角色列表中（除非指定跳过）
    if (
      data.data?.actor_name &&
      !completedMissionActors.value.includes(data.data.actor_name) &&
      !skipAddToCompletedActors
    ) {
      completedMissionActors.value.push(data.data.actor_name)
      console.log('角色完成任务:', data.data.actor_name)
      console.log('当前已完成任务的角色:', completedMissionActors.value)
    }

    // 检查是否已选择角色，如果是，显示完成提示
    // 无论是否跳过添加到已完成任务的角色列表，都需要显示提示
    if (data.data?.actor_name) {
      const resourcesStore = useChatResourcesStore()
      if (
        resourcesStore.currentActorId &&
        resourcesStore.currentActorName === data.data.actor_name
      ) {
        Message.success(
          `This stage is complete. You can still chat with ${data.data.actor_name},or tap 'Finish' to go back.`,
          5000,
        )
      }
    }

    // 处理线索信息
    if (data.data?.clues && Array.isArray(data.data.clues)) {
      const sceneId = data.data.belong_scene_id || data.data.scene_id || ''

      // 添加新线索
      data.data.clues.forEach((clueText: string) => {
        // 检查是否已存在相同的线索，避免重复
        const exists = gameClues.value.some(
          (clue) => clue.text === clueText && clue.scene_id === sceneId,
        )

        if (!exists) {
          gameClues.value.push({
            text: clueText,
            scene_id: sceneId,
          })
        }
      })

      console.log('添加线索:', data.data.clues)
      console.log('当前所有线索:', gameClues.value)
    }
  }

  /**
   * 检查所有角色是否已完成任务
   */
  function checkAllActorsCompletedMission() {
    const resourcesStore = useChatResourcesStore()

    // 如果没有角色选项，则返回 false
    if (!resourcesStore.chatActorOptions?.length) {
      return false
    }

    // 获取所有角色名称
    const actorNames = resourcesStore.chatActorOptions.map(
      (option) => option.actor_name,
    )
    // 检查所有角色是否都完成了任务
    return actorNames.every((name) =>
      completedMissionActors.value.includes(name),
    )
  }

  /**
   * 重置已完成任务的角色列表
   */
  function resetCompletedMissionActors() {
    completedMissionActors.value = []
  }

  /**
   * 返回上一场景并清除事件队列中未处理的场景
   */
  async function jumpToPreviousScene(storyId: string) {
    const messagesStore = useChatMessagesStore()
    const uiStore = useChatUIStore()

    // 清除事件队列中未处理的场景
    messageQueue.value = []
    isProcessingQueue.value = false

    // 清除定时器
    if (countdownInterval.value) {
      clearInterval(countdownInterval.value)
      countdownInterval.value = null
    }

    if (timeoutId.value) {
      clearTimeout(timeoutId.value)
      timeoutId.value = null
    }

    // 清除付费场景计时器
    clearPaidSceneInterval()

    // 清除打字状态Promise,
    if (messagesStore.messageTypingPromise) {
      messagesStore.messageTypingPromise = null
      messagesStore.messageTypingResolve = null
    }

    uiStore.setLoading(true)
    messagesStore.isActorThinking = true
    error.value = null

    try {
      const events: any[] = []
      await new Promise<void>((resolve, reject) => {
        jumpToPreviousSceneSSE(
          storyId,
          async (data) => {
            const parsedData = typeof data === 'string' ? JSONParse(data) : data
            if (parsedData) {
              events.push(parsedData)
            }
          },
          (err) => {
            messagesStore.isActorThinking = false
            error.value = '返回上一场景失败'
            uiStore.setError(true)
            reject(err)
          },
          async () => {
            // Stream closed callback
            messagesStore.isActorThinking = false
            uiStore.setLoading(false)

            // 处理所有事件
            for (const event of events) {
              await handleEvent(event)
            }

            await processEventQueue()
            console.log('返回上一场景成功，事件队列已清除')
            resolve()
          },
        )
      })
      return events
    } catch (err) {
      messagesStore.isActorThinking = false
      uiStore.setLoading(false)
      console.error('返回上一场景失败:', err)
      error.value = '返回上一场景失败'
      throw err
    }
  }

  /**
   * 跳转到下一场景
   */
  async function jumpToNextScene(storyId: string) {
    const messagesStore = useChatMessagesStore()
    const uiStore = useChatUIStore()

    uiStore.setLoading(true)
    messagesStore.isActorThinking = true
    error.value = null

    try {
      const events: any[] = []
      await new Promise<void>((resolve, reject) => {
        jumpToNextSceneSSE(
          storyId,
          async (data) => {
            const parsedData = typeof data === 'string' ? JSONParse(data) : data
            if (parsedData) {
              events.push(parsedData)
            }
          },
          (err) => {
            messagesStore.isActorThinking = false
            error.value = '跳转到下一场景失败'
            uiStore.setError(true)
            reject(err)
          },
          async () => {
            // Stream closed callback
            messagesStore.isActorThinking = false
            uiStore.setLoading(false)

            // 处理所有事件
            for (const event of events) {
              await handleEvent(event)
            }

            await processEventQueue()
            resolve()
          },
        )
      })

      // 重置已完成任务的角色列表
      resetCompletedMissionActors()

      return events
    } catch (err) {
      messagesStore.isActorThinking = false
      uiStore.setLoading(false)
      console.error('跳转到下一场景失败:', err)
      error.value = '跳转到下一场景失败'
      throw err
    }
  }

  /**
   * 处理事件类型分发
   */
  function handleEventByType(event: BaseSSEData) {
    try {
      // 处理任务完成事件
      if (event.event_type === 'mission_completed') {
        console.log('chat4-events: Handling mission completed event')
        handleMissionCompletedEvent(event, false) // 明确指定不跳过添加到已完成任务的角色列表
      }

      // 其他事件类型的处理保持不变
      const result = handleEvent(event)
      return result
    } catch (error) {
      console.error('chat4-events: Error handling event:', {
        eventType: event.event_type,
        error: error.message,
      })
      throw error // 重新抛出错误，让上层处理
    }
  }

  /**
   * 清除付费场景计时器
   */
  function clearPaidSceneInterval() {
    if (paidSceneInterval.value) {
      clearInterval(paidSceneInterval.value)
      paidSceneInterval.value = null
    }
    inPaidScene.value = false
    billingInterval.value = null
  }

  /**
   * 执行pending的场景转换
   */
  /**
   * 清除pending的目标场景
   */
  function clearPendingTargetScene() {
    pendingTargetScene.value = null
  }

  return {
    // 状态
    currentActorId,
    conversationId,
    error,
    messageQueue,
    isProcessingQueue,
    lastSceneTimestamp,
    lastScene,
    currentScene,
    recordedSceneId,
    countdownInterval,
    timeoutId,
    isShouldRestart,
    paymentRequired,
    inPaidScene,
    billingInterval,
    completedMissionActors,
    gameClues,
    isConfirmedLeave,
    showPaidSceneDialog,
    pendingTargetScene,

    // Getters
    hasPendingEvents,
    checkAllActorsCompletedMission,

    // Actions
    handleEvent,
    handleEventByType,
    handleMissionCompletedEvent,
    handleSceneTransitionEvent,
    jumpToPreviousScene,
    jumpToNextScene,
    handleShowSceneEvent,
    handlePaymentRequiredEvent,
    handleErrorEvent,
    handleDebugEvent,
    handleWaitEvent,
    handleUpdateUserCoinsEvent,
    handleUploadImageEvent,
    triggerNextEvent,
    processEventQueue,
    processNextEvent,
    hasNextProcessableEvent,
    processBackgroundEvents,
    handlePlayAudioEvent,
    initializeChat,
    sendMessage,
    clearChat,
    setShouldRestart,
    resetCompletedMissionActors,
    clearPaidSceneInterval,
    startPaidSceneTimer,
    checkAndResetPlaytime,
    clearPendingTargetScene,
    // 场景行为菜单相关
    sceneActionsMenu,
    handleSceneActionsMenuEvent,
    handleShowSceneActionMenuEvent,
    // 好感度系统相关
    favorabilityState,
    handleShowScenesUnlockConditionsEvent,
    handleShowLevelInfoEvent,
    updateFavorabilityLevelInfo,
    updateSceneUnlockStatuses,
    getSceneUnlockStatus,
  }
})
